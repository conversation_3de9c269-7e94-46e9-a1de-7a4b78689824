package com.zte.common.utils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 常量
 *
 * <AUTHOR>  2017年10月26日
 */
public class Constant {

    public static String BYTE_DANCE_NAME = "北京字跳网络技术有限公司";

    public interface RedisKey {
        String OUT_BOUND_CONTRACT_KEY = "outBoundContract:%s";
    }

    public static final String BE_PENDING = "待定";
    public static final Integer ONE_HOUR = 3600000;
    public static final String IN_STOCK = "在库";
    public static final String BILL_CONFIRMED = "已确认";
    public static final String OUT_STOCK = "已出库";

    public static final String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";

    public static final String BYTE_DANCE_TIME = " 23:30:00";
    public static final String REPAIR_DATAIL_EXCEL = "OEM(B2B)产品发货信息查询数据.xls";
    public static final String SHEET1 = "sheet";
    public static final String LOOKUP_TYPE_800033000003 = "800033000003";
    public static final String EMPTY_INPUT_CONTRACT_EG = "合同号、物料代码、物料名称、物料条码，至少输入一个，请重新输入";
    public static final String ONLY_ITEMNAME = "只输入了物料名称，请输入其它精确查询条件或发货日期范围设定到10天以内";

    public static final int INT_50 = 50;

    public static final Integer[] INPUT_TYPE_OF_PARAMS = {1, 2};

    public static final int BATCH_QUERY_SIZE = 500;

    //负数条码监控导出
    public interface ErrorItembarcode {
        String[] PROPS = new String[]{"itemNo", "itemBarcode", "stockNo", "balanceQty", "beginQty", "incomeQty", "outcomeQty"};
        String[] HEADER_LIST = new String[]{"物料代码", "条码", "子库存", "库存数量", "期初数量", "入库数量", "出库数量"};
        String FILE_NAME = "负数条码明细.xlsx";
        String SHEETNAME = "负数条码明细";
        String EMAIL_TITLE_ZH = "包库存-条码负数库存监控 邮件通知";
        String EMAIL_TITLE_EN = "Package inventory - Barcode negative inventory monitoring email notification";
        String EMAIL_PREFIX = "<p style=\"font-family:arial;font-size:30px;\">";
        String EMAIL_PREFIX_A = "<a style=\"color:blue;font-size:25px;font-weight:bold;\" href=";
        String EMAIL_SUFFIX = "</a></p>";
        String EMAIL_COLO = ">";
        String EMAIL_SPLIT = "_";
        String CLICK_DOWN = "请点击下载";
    }

    public static final long DEFAULT_PAGE_SIZE = 200L;
    /**
     * 状态码404
     * 页面未找到
     */
    public static final int FOUR_ZERO_FOUR = 404;

    /**
     * 状态码400
     */
    public static final int FOUR_ZERO_ZERO = 400;
    public static final int TWO_ZERO_ONE = 201;
    public static final String LOCKED_STR = "锁定";
    public static final String MSM_REDIS_LOCK = "正在提交，请稍后再次尝试";
    public static final String BKC = "BKC:";
    public static final String LOOKUP_TYPE_2032 = "2032";
    public static final String LOOKUP_VALUE_2032004 = "2032004";
    public static final String LOOKUP_VALUE_2032005 = "2032005";
    public static final String LOOKUP_VALUE_2032006 = "2032006";
    public static final String LOOKUP_VALUE_2032007 = "2032007";
    public static final String LOOKUP_VALUE_2032009 = "2032009";
    public static final String LOOKUP_VALUE_2032010 = "2032010";
    //包库存条码库存负数收件人
    public static final String LOOKUP_VALUE_2032011 = "2032011";
    public static final String LOOKUP_VALUE_8006038 = "8006038";

    public static final String LOOKUP_VALUE_203009000003 = "203009000003";
    public static final String LOOKUP_VALUE_824009400020 = "824009400020";

    public static final String TABLE_CSS = "<table style='width:1000px;font-family:'微软雅黑';'>";

    public static final String TABLE_ENTITYNAME = "任务号";

    public static final String TABLE_ORG = "组织";

    public static final String TABLE_BILLNO = "装箱单号";
    public static final String TABLE_GROSSWEIGHT = "毛重(公斤)";
    public static final String TABLE_NETWEIGHT = "净重（公斤）";
    public static final String TABLE_LENGTH = "长（毫米）";
    public static final String TABLE_WIDTH = "宽（毫米）";
    public static final String TABLE_HIGH = "高（毫米）";
    public static final String TABLE_CUBUG = "体积（立方米）";

    public static final String APS_ERROR_MONITOR = "包库存-调用存储过程回写计划异常";
    public static final String ITEMBARCODE_ERROR_MONITOR = "包库存-条码负数库存监控";
    public static final String BKC_UPDATE_STEP_STOCK_LOCKED = "包库存-更新条码和物料库存正在执行";

    public static final String BKC_ZT = "BKC_ZT:";
    public static final String STR_102 = "102";
    public static final String XBRK_YC_XBRK_YA = "XBRK_YC,XBRK_YA";
    public static final String XBZK_YC = "XBZK_YC";
    public static final String STRING_SPLITPAGE = "SPLITPAGE";
    public static final String STRING_TOTALNUMBER = "TOTALNUMBER";

    public static final String PARENT_SN = "parentSn";
    public static final String DELIVERY = "配送";
    public static final String STR_01 = "01";
    public static final String STR_03 = "03";


    /**
     * 系统类型
     */
    public static final String LIST_TYPE_MES = "mes+";
    public static final String FLTB = "FLTB";
    public static final String ZHFLTB = "ZHFLTB";

    public static final String MSA_DATA_SOURCE = "msaDataSource";

    public static final String FLAG_Y = "Y";

    public static final String STR_ITEM_BARCODE_GT4000 = "STR_ITEM_BARCODE_GT4000";

    public static final String FLAG_N = "N";

    public static final String STR_PROCESSSTATUS_START = "<ProcessStatus>";
    public static final String STR_BILLNO_START = "<BillNo>";
    public static final String STR_BILLNO_END = "</BillNo>";
    public static final String STR_DEALER_START = "<Dealer>";
    public static final String STR_DEALER_END = "</Dealer>";
    public static final String STR_ERRORMESSAGE_START = "<ErrorMessage>";
    public static final String STR_ERRORMESSAGE_END = "</ErrorMessage>";

    public static final String FLAG_BOTH = "B";

    public static final String SPM_OFFLINE_SWITCH = "6900";

    public static final String DATE_FORMATER_YYYYMMDD = "yyyy-MM-dd";

    public static final String LOCAL_RESOLVER = "localeResolverMes";

    public static final String[] PDF_HEADS = new String[]{"销售订单", "销售领料单", "发货仓库", "SO单号", "物料代码"
            , "物料条码", "落放ID", "拟领数量", "发货数量", "班组", "用途", "物料名称", "领料人", "领料单位"};

    public static final String[] EXCEL_TITLE_STB_DELIVERY_INFO_QUERY = new String[]{
            "合同号", "发货指令号(DO单)", "任务发货日期", "装箱单号", "物料条码(产品SN)",
            "物料代码", "物料名称", "生产单位", "生产日期", "品牌",
            "设备种类", "型号", "生产任务号(外协)", "制造工艺单号", "MAC1",
            "MAC2", "单板SN1", "单板SN2", "产品SN", "纸箱SN",
            "栈板SN", "电源SN", "遥控器SN", "FT测试结果", "FT测试时间",
            "老化测试结果", "老化测试时长", "MAC配置结果", "MAC配置时间", "开关机测试结果",
            "开关机测试时间", "整机测试结果", "整机测试时间", "整机校验结果", "征集校验时间",
            "出厂配置结果", "出厂配置时间", "软件版本号", "Logo版本号", "出厂配置文件名",
            "备注1", "备注2", "备注3", "备注4", "备注5",
            "备注6", "备注7", "导入人", "导入时间", "最后更新时间", "进网标志号", "进网扰码",
            "数字编码", "产地"};

    public static final String[] EXCEL_PROPS_STB_DELIVERY_INFO_QUERY = new String[]{
            "mainContractNo", "frInsNo", "issueDate", "billNumber", "barcode",
            "itemNo", "itemName", "productionUnit", "productionDate", "brand",
            "deviceType", "model", "productionEntityno", "produceBillno", "mac1Addr",
            "mac2Addr", "boardSn1", "boardSn2", "productSn", "cartonSn",
            "palletSn", "powersourceSn", "remotecontrolSn", "testResult", "testTime",
            "ageingTestresult", "ageingTesttime", "macConfigresult", "macConfigtime", "onoffTestresult",
            "onoffTesttime", "machineTestresult", "machineTesttime", "machineCheckresult", "machineChecktime",
            "facConfigresult", "facConfigtime", "softVerNum", "logoVerNum", "facConfigfile",
            "remark1", "remark2", "remark3", "remark4", "remark5",
            "remark6", "remark7", "createdFullBy", "creationDate", "lastUpdateDate", "netAccessSignNum",
            "scrambleCode", "nasn", "madeIn"};

    public static final String PDF_TITLE = "康讯发货清单";

    public static final String CFG_RULE_ITEM_TYPE_FIXEDVALUE = "302000300001";

    public static final String CFG_RULE_ITEM_TYPE_SEQUENCE = "302000300002";

    public static final String CFG_RULE_ITEM_TYPE_DATE = "302000300003";

    public static final String MFG_SITE_TYPE = "主设备";

    public static final long REDIS_MONTH_EXPIRE = 60 * 60 * 24 * 31;
    public static final long REDIS_YEAR_EXPIRE = 60 * 60 * 24 * 31 * 12;

    //批量操作每次的数量
    public static final int SPLITSIZE_TEN = 10;
    public static final int SPLITSIZE_FIFTY = 50;
    public static final int SPLITSIZE_HUNDRED = 100;
    public static final int BOM_LENGTH = 12;

    public static final String COMMA = ",";

    public static final String STR_PO = "PO";
    public static final String STR_FORMAL = "正式";
    public static final String STR_PREDICT = "预估";
    public static final String STR_STANDARD = "标准合同订单";
    public static final String STR_NON_STANDARD = "非标准合同订单";
    public static final String MEITUAN_ERROR_PROCEDURES = "美团关键工序缺失：";
    public static final String MEITUAN_ERROR_LOG_PROCEDURES = "美团日志关键工序缺失：";
    public static final String MEITUAN_ERROR_QUALITY_CODE = "美团质量码缺失：";
    public static final String MDS_ERROR_TOKEN = "获取中试Token出错：";
    public static final String MDS_ERROR_API = "调用中试API出错：";
    public static final String SINGLE_QUOTE = "'";
    public static final String JSON_BO = "bo";
    public static final String JSON_CODE = "code";
    public static final String SUCCESS_CODE = "0000";
    public static final String BUSINESS_ERROR_CODE = "0005";
    public static final String WORK_ORDER_SOURCE_STEP = "STEP";
    public static final String WORK_ORDER_SOURCE_WMES = "WMES";
    public static final long NUMBER_ZREO = 0;

    public static final String X_FACTORY_ID = "X-Factory-Id";
    public static final int NUMBER_2 = 2;
    public static final int NUMBER_3 = 3;
    public static final int NUMBER_26 = 26;
    public static final int NUMBER_16 = 16;

    public static final int NUMBER_990 = 990;
    public static final String CENTER_LINE = "-";
    public static final String TYPE_SEQUENCE_CODE = "序列码";
    public static final String TYPE_BATCH_CODE = "批次码";
    public static final String HORIZON = "-";
    public static final String STRING_EMPTY = "";
    public static final String STRING_SPACE = " ";
    public static final String STRING_MAOHAO = ":";

    public static final String DOUHAO = "，";
    public static final String RESULT_CURSOR = "resultCursor";
    public static final String INFO_CURSOR = "infoCursor";
    public static final String INDEX = "index";
    public static final String PLAN_ID = "planID";
    public static final String NODE_ID = "nodeID";
    public static final String BOARD_CODE = "boardCode";
    public static final String DATA = "data";

    public static final String INDEX_STR = " 序号";
    public static final String TASK_NO = "任务";
    public static final String ERROR_INFO = "错误信息";

    public static final String BOXNUMBER = "boxNumber";
    public static final String MD5 = "md5";

    public static final String SOURCE_CODE = "KX ENTRY";

    public static final String TRANSACTION_TYPE_ID = "44";

    public static final String REASON_ID = "30";

    public static final String SOURCE_TATLE_NAME_WIP_MOVE = "ZTE_WIP_MOVE_MTL_TXN";

    public static final String IMES = "IMES";


    public static final String SOURCE_TABLE_DETAIL_ID = "TRANSACTION_ID";

    public static final String TRANSACTON_TYPE_FL = "FL";

    public static final String OPER_SEQ_NUM = "5";

    public static final String SOURCE_TATLE_NAME_MRP_WIP = "ZTE_MRP_WIP_ISSUE";

    public static final String STR_NUMBER_ONE = "1";

    public static final String STR_NUMBER_TWO = "2";

    public static final String STR_NUMBER_ZERO = "0";

    public static final String STR_NUMBER_THREE = "3";

    public static final String STR_NUMBER_FOUR = "4";
    public static final String STR_NUMBER_FIVE = "5";
    public static final String STR_NUMBER_SIX = "6";

    public static final String POINT = ".";


    public static final int INT_1 = 1;
    public static final int INT_B1 = -1;
    public static final int INT_0 = 0;
    public static final int INT_6 = 6;
    public static final int INT_7 = 7;
    public static final int INT_20 = 20;

    public static final BigDecimal BIG_42 = new BigDecimal("42");
    public static final BigDecimal BIG_601 = new BigDecimal("601");
    public static final BigDecimal BIG_612 = new BigDecimal("612");
    public static final BigDecimal BIG_613 = new BigDecimal("613");

    public static final String[] PKG_BATCH_SCAN_VALID_FIELD = new String[]{
            "boxNo", "taskNo", "itemName", "itemType", "itemCode"};

    public static final String CODE_SUCCESS = "\"code\":\"0000\",";
    public static final String ERROR_REASON_START = "\"msg\":";
    public static final String ERROR_REASON_END = "},\"bo\":";
    public static final String POSITIONID_END = ",\"other\":{";
    public static final String LEVER_INFO_LIST_TOTAL = "leverInfoListToTal";
    public static final String MARK_INFO_LIST_TOTAL = "markInfoListTotal";
    public static final String ORG_ID = "orgId";
    public static final String X_ENTITY_ID = "xEntityId";
    public static final String X_BILL_ID = "xBillId";
    public static final String X_ITEM_ID = "xItemId";
    public static final String X_EXIT = "xExit";
    public static final String X_REL_EXIT = "xRelExit";
    public static final String X_ISREPEAT = "xIsrepeat";
    public static final String START_SN = "START_SN";
    public static final String END_SN = "END_SN";
    public static final String CNT = "CNT";
    public static final String P_MESSAGE = "pMessage";

    public static final String GET_LPN_ERROR = "获取栈板信息失败";
    public static final String JSON_MSG = "msg";

    public static final String SUBMITTED = "SUBMITTED";
    public static final String X_SUBMACHINE_NUMBER = "xSubmachineNumber";
    public static final String X_BOX_NO = "xBoxNo";
    public static final String X_ITEM_TYPE = "xItemType";
    public static final String X_NUM = "xNum";
    public static final String X_ITEM_BARCODE = "xItemBarcode";
    public static final String X_ITEM_BARCODE_LIST = "xItemBarcodeList";
    public static final String X_ITEM_CODE = "xItemCode";
    public static final String X_ITEM_NAME = "xItemName";
    public static final String X_ITEM_UNIT = "xItemUnit";
    public static final String X_CREATE_BY = "xCreatedBy";
    public static final String X_CONFIGDETAIL_ID = "xConfigdetailId";
    public static final String X_PARENTCONFIGDETAIL_ID = "xParentconfigdetailId";
    public static final String X_PARENTNUM = "xParentnum";
    public static final String X_PARENTITEM_ID = "xParentitemId";
    public static final String X_PARENTITEM_CODE = "xParentitemCode";
    public static final String X_PARENTITEM_NAME = "xParentitemName";
    public static final String X_PARENTITEM_UNIT = "xParentitemUnit";
    public static final String X_TYPE = "xType";
    public static final String X_BAR_ITEMCODE = "xBarItemcode";
    public static final String X_BATCH_ID = "xBatchId";

    public static final String NULL = "null";

    public static final String BIG_NULL = "NULL";
    public static final String PERIODIC_CALCULATION_FAILURE = "定时系统单板周期计算单板失败 --> BSN:";
    public static final String INSERT_PRODPKANID_FAILURE = "定时系统单板周期重复插入批次数据到数据库 --> PRODPLAN_ID:";
    public static final String UPDATE_PRODLANID_FAILUERE = "定时系统单板周期更新批次数据到数据库失败 --> PRODPLAN_ID:";
    public static final String UPDATE_BOM_FAILE = "定时系统单板周期更新BOM数据到数据库失败 --> BOM_NO:";
    public static final String CALCULATING_THE_PERIODIC_PREDICTION_OUTPUT = "部件预测产出周期计算任务";
    public static final String INSERT_BAORD_PERIODIC = "定时系统单板周期重复插入单板周期计算记录到数据库 --> calcStartDate:";
    public static final String PERIOD_CALCULATING_THREAD = "周期计算线程";
    public static final String DATA_MERGING_THREAD = "数据合并线程";
    public static final String BOARD_ONLINEOLD = "board_onlineold";
    public static final String SCAN_BOARDONLINE = "scan_boardonline";
    public static final String STBEXCELBIGDATAEXPORT = "StbExcelBigDataExport_";
    public static final String BAR_SUBMIT_KEY = "BAR_SUBMIT_";
    public static final String INPUTQTY = "InputQty";
    public static final String TASKQTY = "TaskQty";
    public static final String PACKTASKQTY = "PackTaskQty";
    public static final String THREEMONTH = "ThreeMonth";
    public static final String LASTYEATMONTH = "LastYearMonth";
    public static final String WMESPRODUCTIONINFOBOARDLASTYEAROF = "WmesProductionInfoBoardLastYearOf";
    public static final String BEFORETHREEMONTH = "BeforeThreeMonth";
    public static final String WMESPRODUCTION = "WmesProductionInfoBoardThisYearOfEarlyMonths";
    public static final String PRODUCTCATEGORYNAME = "productCategoryName";
    public static final String NONE = "无";
    public static final String ISCP = "ISCP";
    public static final String SYSTEM = "SYSTEM";
    public static final String INFOR = "INFOR";
    public static final String SC = "SC";
    public static final String NO_DATA = "没有数据";
    public static final String OID_COUNT_GT20 = "oid数量不能大于20";
    public static final String EXCUTE_SUCCESS = "执行成功";
    public static final String OPERATE_SUCCESS = "操作成功";

    public static final String LOOUP_TYPE_2030100 = "2030100";
    public static final String LOOKUP_TYPE_1004037 = "1004037";
    public static final String LOOKUP_CODE_1004037026 = "1004037026";
    /**
     * 归档最大失败次数
     */
    public static final String LOOKUP_CODE_824005000001 = "824005000001";
    /**
     * 是否正在归档标识
     */
    public static final String LOOKUP_CODE_824005000002 = "824005000002";
    /**
     * 是否允许执行归档任务标识
     */
    public static final String LOOKUP_CODE_824005000003 = "824005000003";
    /**
     * 杂项交易明细需归档子库
     */
    public static final String LOOKUP_CODE_824005000004 = "824005000004";
    /**
     * 杂项交易明细需归档物料代码
     */
    public static final String LOOKUP_CODE_824005000005 = "824005000005";
    /**
     * 是否正在同步数据
     */
    public static final String LOOKUP_CODE_824005000006 = "824005000006";

    public static final String BILLTYPE_OR_BILLNUMBER_IS_NULL = "业务类型或单据号不能为空";
    public static final String ROW_ID = "ROW_ID";
    public static final String ROWID = "ROWID";
    public static final String TABLE_NAME_IS_NULL = "表名不能为空";
    public static final String TABLE_PK_IS_NULL = "该表没有主键，不允许执行更新或删除操作";
    public static final String CONDITION_MUST_HAS_PK = "操作条件必须包含主键";

    public static final String CURRENT_DATE = "当前时间：";
    public static final String CALCULATE_PROCESS_ERROR = "计算过程出现异常，异常为: ";
    public static final String SET_PARAMS_ERROR = "setParams异常:";
    public static final String PRODUCT_DATA_SUCCESS = "生成数据成功！";
    public static final String DELETE_HISTORY_DATA_SUCCESS = "删除历史数据成功！";
    public static final String STR_BO = "bo";
    public static final String BOX_LOOK_UP_TYPE = "10260547";
    public static final String BOX_LOOK_UP_CODE_ONE = "10260547001";
    public static final String BOX_LOOK_UP_CODE_TWO = "10260547002";
    public static final String BOXSIZE_LOOK_UP_CODE = "303010000002";

    public static final String JDPAGESIZE_LOOK_UP_CPDE = "800622200001";

    public static final String CSG_URL_LOOK_UP_CODE = "202241200001";

    public static final String CSG_PAGESIZE_LOOK_UP_CODE = "202241300001";

    public static final String LOOK_UP_CODE_302003700001 = "302003700001";

    public static final String LOOK_UP_CODE_302003700002 = "302003700002";

    public static final String LOOK_UP_CODE_302003700003 = "302003700003";

    public static final String LOOK_UP_CODE_302003700006 = "302003700006";
    public static final String ICC_URL_LOOK_UP_CODE = "303010100010";
    public static final String MODEL_PARAM_BOXSIZE_CODE = "303010000003";
    public static final String ENTRY_LOOK_UP_TYPE = "20210709";
    public static final String ENTRY_LOOK_UP_CODE_ONE = "20210709001";
    public static final String ENTRY_LOOK_UP_CODE_TWO = "20210709002";

    public static final String LOOK_UP_800604200001 = "800604200001";
    public static final String LOOK_UP_800604200002 = "800604200002";
    public static final String LOOK_UP_800604200003 = "800604200003";
    public static final String LOOK_UP_800604200004 = "800604200004";

    public static final String ENTRY_KEY_ID = "KEY_ID";
    public static final String ENTRY_KEY_TIME = "LAST_UPDATE_DATE";
    public static final int MODEL_PARAM_BOXSIZE_INIT = 500;
    public static final int MES_KAFKA_ERROR_MSG_SIZE = 500;
    public static final int INT_5000 = 5000;
    public static final int INT_500 = 500;
    public static final int INT_100 = 100;
    /* Started by AICoder, pid:k40b1603606df8214a5f08af80a6e103c4e6c2d4 */
    public static final int INT_10000 = 10000;
    /* Ended by AICoder, pid:k40b1603606df8214a5f08af80a6e103c4e6c2d4 */
    public static final int INT_10 = 10;
    public static final String CURRENT_NO = "CurrCount";
    public static final String STRING_C = "C";
    public static final String STRING_B = "B";
    public static final String GETMAC = "GETMAC(";
    public static final String SMAC_SPLIT = "'-'";

    public static final String LOOKUPTYPE = "lookupType";
    //INFOR发料同步数据字典
    public static final String LOOKUPTYPE_2024 = "2024";
    public static final String STR_A = "A";
    public static final String STR_C = "C";
    public static final String X_LANG_ID = "X-Lang-Id";
    public static final String ZH_CN = "zh_CN";
    public static final String BARCODE = "barcode";

    public static final String ZTE_EN = "ZTE";
    public static final String ZTE_CN = "中兴";

    public static final String ZTE_CORPORATION = "ZTE-中兴智能科技南京有限公司";

    public static final String MCT_CAPTURE = "MCT_CAPTURE";
    public static final String X_EMP_NO = "X-Emp-No";
    public static final String X_AUTH_VALUE = "X-Auth-Value";
    public static final String SYS_X_VALUE = "2024003";
    public static final String SYS_WMS_ADD = "2024002";
    public static final String SYS_WMS_ADD_DEFAULT = "http://apiwms.sc.zte.com.cn/zte-iss-stock-center/task-detail/syncIncomingDetailsForLine";
    public static final String SYS_TIME_GAP = "2024001";
    public static final String SYS_TASK_GAP = "2024004";
    public static final String SYS_TASK_SUCCESS = "2024006";

    public static final String SYS_TASK_ZHFLTB = "2024007";

    public static final String SYS_WMS_ADD_FOR_ZH = "2024008";

    public static final String SYS_WMS_TIME_GAP_FOR_ZH = "2024009";

    public static final String SYS_WMS_ADD_FOR_ZH_DEFAULT = "http://apiwms.sc.zte.com.cn/zte-iss-stock-center/task-detail/syncIncomingDetailsForZHLine";
    public static final String SYS_TASK_GAP_19 = "-19";
    public static final String TASKTYPECODE_VALUE = "ASN";
    public static final String TASKTYPECODE = "taskTypeCode";


    public static final String ISALLSUCCESS = "isAllSuccess";
    public static final String SYNCFAILEDINCOMINGDETAILS = "syncFailedIncomingDetails";

    //请求头
    public static final String SRCBILLNO = "srcBillNo";
    public static final String REQLINEINDEX = "reqLineIndex";
    public static final String TASKDETAIL = "taskDetail";
    //请求明细
    public static final String ABCITEMNO = "abcItemNo";
    public static final String BARCODETYPE = "barcodeType";
    public static final String FROMWAREHOUSEID = "fromWarehouseId";

    public static final String ITEMNO = "itemNo";
    public static final String LEADPROPERTIES = "leadProperties";
    public static final String LFID = "lfid";
    public static final String LINEINDEX = "lineIndex";
    public static final String PLANTRACKINGBATCH = "planTrackingBatch";

    public static final String REELID = "reelid";
    public static final String REQQTY = "reqQty";
    public static final String STOCKTYPECODE = "stockTypeCode";
    public static final String TTBARCODE = "ttBarcode";
    public static final String STR_UUID = "uuid";
    public static final String ALLOWOVERPICK = "allowOverPick";
    public static final String ORI_BOX_NO = "oriBoxNo";

    public static final String WAREHOUSETYPE = "warehouseType";
    public static final String XBC = "线边仓";

    public static final String LIST_STR = "list";


    public static final String STARTROW = "startRow";
    public static final String ENDROW = "endRow";

    public static final String YES = "是";

    public static final String NO = "否";

    public static final String STR_ROWS = "rows";
    public static final String STR_PAGE = "page";


    public static final Long PAGE = 1L;
    public static final Long ROWS = 10L;
    public static final String KEY_START_ROW = "startRow";
    public static final String KEY_END_ROW = "endRow";
    public static final String KEY_ITEM_CONTACT = "contact";
    public static final String KEY_BOX_NO = "boxNo";
    public static final String KEY_FREIGHT_NO = "frInsNo";

    public static final String KEY_S = "S";
    public static final int INT_8 = 8;
    public static final int INT_200 = 200;

    public static final int INT_4000 = 4000;
    public static final int INT_1000 = 1000;
    public static final String QUERY_TYPE_C = "C";
    public static final long THREE_NUMBER = 3;

    public static final String LOOKUP_TYPE_8000320 = "8000320";
    public static final String LOOKUP_TYPE_800032000001 = "800032000001";
    public static final String LOOKUP_TYPE_800032000002 = "800032000002";
    public static final String LOOKUP_TYPE_800032000003 = "800032000003";
    public static final String LOOKUP_TYPE_800032000004 = "800032000004";
    public static final String LOOKUP_TYPE_800032000005 = "800032000005";
    public static final String LOOKUP_TYPE_800032000006 = "800032000006";
    public static final String LOOKUP_TYPE_800032000007 = "800032000007";
    public static final String STRING_G = "G";
    public static final String STRING_ESB = "ESB";
    public static final String STRING_DATA = "DATA";
    public static final String STRING_DATAINFOS = "DATAINFOS";
    public static final String STRING_DATAINFO = "DATAINFO";
    public static final String STRING_SUCCESS = "successful";
    public static final String STRING_PART_NO = "PART_NO";
    public static final String STRING_MAX_WEIGHT = "MAX_WEIGHT";
    public static final String STRING_DESC2 = "DESC2";
    public static final String STRING_DESC4 = "DESC4";
    public static final String STRING_DESC5 = "DESC5";
    public static final String KEY_E = "E";
    public static final int INT_3 = 3;
    public static final int INT_4 = 4;


    public static final String LOOKUP_TYPE_8000330 = "8000330";
    public static final String LOOKUP_TYPE_800033000001 = "800033000001";
    public static final String LOOKUP_TYPE_800033000002 = "800033000002";

    public static final String LOOKUP_TYPE_8006119 = "8006119";

    public static final String LOOKUP_TYPE_800611900001 = "800611900001";

    public static final String LOOKUP_TYPE_8006120 = "8006120";

    public static final String LOOKUP_TYPE_800612000001 = "800612000001";
    public static final String LOOKUP_TYPE_824009500002 = "824009500002";
    public static final String LOOKUP_TYPE_824005900009 = "824005900009";

    public static final String LOOKUP_TYPE_8240094 = "8240094";

    public static final String LOOKUP_TYPE_824009400030 = "824009400030";
    public static final String LOOKUP_TYPE_824009400031 = "824009400031";
    public static final String LOOKUP_TYPE_824009400009 = "824009400009";
    public static final String LOOKUP_TYPE_824009400006 = "824009400006";
    public static final String LOOKUP_TYPE_824009400010 = "824009400010";
    public static final String LOOKUP_TYPE_824009400011 = "824009400011";
    public static final String PICKTYPE_CJK = "车间库";
    public static final String ALI_CATEGORY = "业务分类";
    public static final String ALI_MATERIAL_BOM= "物料BOM";
    public static final String ALI_MATERIAL_NAME= "物料名称";
    public static final String ALI_MATERIAL_BILL_LIST= "发料清单";
    public static final String ALI_BILL_DETAIL_LIST= "发料详情";

    public static final String STR_APP_CODE = "appcode";

    public static final String SCM_URL = "http://srm.zte.com.cn/sppt/eai/GetOutCheckInfoSrv.asmx";
    public static final String JAVA_CACL = "JAVA_CACL_";
    public static final String DONE = "已完成";
    public static final String POINT_P = "\\.";
    public static final double LONG_0 = 0;
    public static final double LONG_3000 = 3000;
    public static final double LONG_999 = 9999.9;
    public static final double LONG_X1 = -1;
    public static final int INT_2 = 2;
    public static final double LONG_20 = 20;
    public static final String DOT_T = ".";
    public static final String DOT_N = "N";
    // 邮件公共服务地址
    public static final String LOOK_1004073 = "1004073";
    public static final String LOOK_1004073001 = "1004073001";

    public static final String MFG_SITE = "按站址发货";
    public static final String Q_ZERO = "Q000000000000";
    public static final String POINT_S = "/";


    public static final String LOOK_3030071 = "3030071";
    public static final String LOOK_3030073 = "3030073";
    public static final String DEPT_NO = "无线";

    public static final String DQAS1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?>";
    public static final String DQAS2 = "<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"";
    public static final String DQAS3 = " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"";
    public static final String DQAS4 = " xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">";
    public static final String DQAS5 = "<soap12:Body>";
    public static final String DQAS6 = "<GetWLBOQControlResult  xmlns=\"http://mte_dqas.zte.com.cn/\">";

    public static final String DQAS7 = "<AgreeNo>";
    public static final String DQAS8 = "<TaskNo>";
    public static final String DQAS9 = "<BoxListNo>";
    public static final String DQAS10 = "<ItemNo>";
    public static final String DQAS11 = "<Scanman>";
    public static final String DQAS12 = "<Partcode>";

    public static final String DQAS77 = "</AgreeNo>";
    public static final String DQAS88 = "</TaskNo>";
    public static final String DQAS99 = "</BoxListNo>";
    public static final String DQAS100 = "</ItemNo>";
    public static final String DQAS111 = "</Scanman>";
    public static final String DQAS122 = "</Partcode>";
    public static final String LOOKUP_TYPE_800032000009 = "800032000009";
    public static final String DQAS13 = "<Tfpid>";
    public static final String DQAS133 = "</Tfpid>";

    public static final String DQAS14 = "<FailReason>" + "" + "</FailReason>";
    public static final String DQAS15 = "</GetWLBOQControlResult>";
    public static final String DQAS16 = "</soap12:Body>";
    public static final String DQAS17 = "</soap12:Envelope>";

    public static final String DQAS_LOOK_UP_TYPE = "6300";
    public static final String DQASHEAD = "<?xml version=\"1.0\" encoding=\"utf-8\"?>";
    public static final String DQASSOAP = "<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"";
    public static final String DQASXSD = " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"";
    public static final String DQASXMLNS = " xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">";
    public static final String DQASBODY = "<soap12:Body>";
    public static final String DQASURL = "<GetWLBOQControlResult  xmlns=\"";
    public static final String DQASHTTP = "\">";

    public static final String DQASAGREENO = "<AgreeNo>";
    public static final String DQASTASKNO = "<TaskNo>";
    public static final String DQASBOXLISTNO = "<BoxListNo>";
    public static final String DQASITEMNO = "<ItemNo>";
    public static final String DQASSACNMAN = "<Scanman>";
    public static final String DQASPARTCODE = "<Partcode>";

    public static final String DQASAGREENOT = "</AgreeNo>";
    public static final String DQASTASKNOT = "</TaskNo>";
    public static final String DQASBOXLISTNOT = "</BoxListNo>";
    public static final String DQASITEMNOT = "</ItemNo>";
    public static final String DQASSACNMANT = "</Scanman>";
    public static final String DQASPARTCODET = "</Partcode>";

    public static final String DQASIFPID = "<Tfpid>";
    public static final String DQASIFPIDT = "</Tfpid>";

    public static final String DQASREASON = "<FailReason>" + "" + "</FailReason>";
    public static final String DQASMETHOD = "</GetWLBOQControlResult>";
    public static final String DQASBODYT = "</soap12:Body>";
    public static final String DQASSOAPT = "</soap12:Envelope>";


    public static final String CHARSETNAME = "utf-8";
    public static final String CONTENTNAME = "application/soap+xml;charset=utf-8";
    public static final String HEADNAME = "SOAPAction";
    public static final String STRING_MARK = "mark";
    public static final String STRING_IS_STOP = "isStop";

    public static final String ENTITY_NO = "entityNo";
    public static final String P_BARCODE = "pBarcode";

    public static final String PRE_ZS = "ZS";
    public static final String PRE_220 = "220";
    public static final String ORG_INAZATION = "orginazation";
    public static final String P_INDEX = "pIndex";
    public static final String P_IS_BARCODE = "pIsBarcode";
    public static final String P_ITEM_BARCODE = "pItemBarcode";
    public static final String P_ORG_ID = "pOrgId";
    public static final String P_BEGIN_DATE = "pBeginDate";
    public static final String P_END_DATE = "pEndDate";
    public static final String P_COUNT = "pCount";
    public static final String WEB_REQUST_HEADER = "<?xml version=\"1.0\" encoding=\"utf-8\"?>";
    public static final String WEB_SRVICE_ADDRESS = "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"";
    public static final String WEB_SRVICE_ADDREST = " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"";
    public static final String WEB_SRVICE_ADDRESH = " xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">";
    public static final String WEB_SRVICE_ADDRESX = "<soap:Body>";
    public static final String WEB_SRVICE_ADDRESW = "<getSparePartInfoList  xmlns=\"http://ecc.zte.com.cn/\">";
    public static final String WEB_SRVICE_ADDRESV = "<in0>";
    public static final String WEB_SRVICE_ADDRESB = "</in0>";
    public static final String WEB_SRVICE_ADDRESA = "</getSparePartInfoList>";
    public static final String WEB_SRVICE_ADDRESE = "</soap:Body>";
    public static final String WEB_SRVICE_ADDRESC = "</soap:Envelope>";
    public static final String WEB_REQUEST = "http://ecc.zte.com.cn/service/services/SparePartInfoWebservice?wsdl";
    public static final String WEB_CHART = "utf-8";
    public static final String WEB_CHART_ADD = "application/soap+xml; charset=utf-8";
    public static final String WEB_TEXT_XML_ADD = "text/xml;charset=utf-8";

    public static final String WEB_TEMP_URI = "<GetOutCheckInfo xmlns=\"http://tempuri.org/\">\n";

    public static final String CONTENT_TYPE = "Content-type";
    public static final String ACCEPT = "Accept";

    public static final String PACK = "包装";
    public static final String WEB_HEADER = "SOAPAction";
    public static final String WEB_MAIN = "<sparepartinfolist>";
    public static final String WEB_BODY = "</sparepartinfolist>";
    public static final String WEB_ITEM_CODE = "<ITEM_CODE>";
    public static final String WEB_ITEM_CODE_END = "</ITEM_CODE>";
    public static final String WEB_ITEM_NAME = "<ITEM_NAME>";
    public static final String WEB_ITEM_NAME_END = "</ITEM_NAME>";
    public static final String WEB_ITEM_ID = "<ITEM_ID>";
    public static final String WEB_ITEM_ID_END = "</ITEM_ID>";

    public static final String P_RESULT = "pResult";
    public static final String EMPTY_NO_NO = "";
    public static final String SEQUENCE_NAME = "序列码";
    public static final String FLAGS_Y = "Y";
    public static final String CONFIG_ASSEMBLE_D = "配置物料装配绑定";
    public static final String ZERO_STRING = "0";
    public static final int THREE_NUMBER_NAME = 3;
    public static final int FOUR_NUMBER_NAME = 4;
    public static final int TOW_NUMBER_NAME = 2;
    public static final String ZERO_NUMBER_STRING_DT = "-1";
    public static final String STR_TWO_NEGATIVE = "-2";
    public static final String DEFAULT_Y = "Y";
    public static final long SIXTY_TIME = 60;
    public static final long ONE_THOUSAND = 1000;
    public static final long FOUTY_TIME = 24;
    public static final long ONE_HUNDRED_TIME = 180;
    public static final String DATE_FORMATE_DAY = "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_FORMAT_DAY2 = "yyyy-MM-dd HH:mm:ss";
    public static final String PDM_JOB_NAME = "PDM_JOB_NAME";
    public static final long FINAL_THREE_MININUTE = 30 * 60000;
    public static final int ONE_FINAL_NUMBBER = 1000;
    public static final String TIME_FOMART = "yyyy-MM-dd HH:mm:ss";
    public static final String HAVE_SOME = "有";
    public static final String MES_HAVE_SOME = "MES";
    public static final String HAVE_BO = "bo";
    public static final String URL_FORMAT = "UTF-8";
    public static final String URL_JSON_FORMAT = "application/json";
    public static final String PDM_WORD_ONE = "receiveResult";
    public static final String PDM_WORD_TWO = "receiveMessage";
    public static final String DATA_DIC = "20210818";
    public static final String PDM_SEND_SUCESS = "S";
    public static final String PDM_SEND_MEMO = "无";
    public static final String PDM_SEND_FAILURE = "F";
    public static final String PDM_SEND_INFORDT = "发送失败";
    public static final String PDM_URL_ADRESS = "http://test.pdmweb.zte.com.cn/zte-plm-pdm-api/receive/assemblyInfo";
    public static final String NO_FLAGS = "N";
    public static final String YES_FLAGS = "Y";
    public static final String ME_MO_FLAGS = "meMo";

    public static final String STRING_IS_INFO = "isInfo";
    public static final String STRING_NORMAL_END = "正常结束";
    public static final String SCAN_FLAG = "SCAN_FLAG";
    public static final String STRING_BOXUP_FLAG = "BOXUP_FLAG";
    public static final String STRING_YY = "yy";

    public static final String PDVM = "PDVM";
    public static final String STRING_STOP = "stop";
    public static final String STRING_ZC = "zc";
    public static final String STRING_XN = "xn";

    public static final String STRING_GE_EN = "GE";
    public static final String STRING_IS_INFOC = "isInfoC";
    public static final String STRING_CN = "中文";
    public static final String STRING_US = "英文";
    public static final String STRING_VR = "虚拟箱";
    public static final String STRING_Z = "Z";
    public static final String STR_TWENTY = "20";


    public static final String STR_MAIN_MATERIAL = "主物料";
    public static final String STR_IN_OTHER = "部分物料在其他箱中";

    public static final String STR_REX = "^[0-9]*$";
    public static final String BILL_ID = "BILL_ID";
    public static final String BILL_NUMBER = "BILL_NUMBER";

    public static final String STR_STATUES = "生效";

    public static final long CREATED_BY = 999;
    public static final String CRETED_NAME = "9999";
    public static final long KAFKA_RAPEAT = 0;
    public static final long KAFKA_STATU = 0;
    public static final long FIVE_MINUTE_TIME = 5 * 60000;
    public static final int HUNDRED_KAFKA = 100;
    public static final String KEY_NAME_BOX = "billNumber";
    public static final String PCREATDD_BY = "pCreatddBy";
    public static final String KAFKA_NAME_JOB = "KAFKA_MES_BOX";
    public static final long FIVE_TIME_JOB = 5 * 60 * 60000;
    public static final long FIFITY_TIME_JOB = 15 * 60 * 60000;
    public static final long KAFKA_STATUS = 0;

    public static final String BARCODE_PRICE_JOB_NAME = "BARCODE_PRICE";

    public static final String MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME = "MEITUAN_PRODUCTDATAUPLOAD";

    public static final String LOOKUP_TYPE_824005600001 = "824005600001";
    public static final String MD5_STATUS = "";

    public static final int MATH_DIGITAL = 999;

    public static final String CREATE_NAME = "9999";

    public static final int KAFKA_DIGTAL = 1;
    public static final String STR_TAB = "\t";
    public static final String SINGLE_QUOTES = "'";

    public static final String IMES_PREFIX = "imes_";
    public static final String PRODUCTIONMGMTSYS_PREFIX = "zte_mes_manufactureshare_sys_";
    public static final String SEND_WIP_EXTENDIDENTIFICATION_TO_PDM = IMES_PREFIX + PRODUCTIONMGMTSYS_PREFIX + "send_wip_extendidentification_to_pdm_";
    public static final String LOOKUP_TYPE_6699 = "6699";
    public static final String LOOKUP_TYPE_6699001 = "6699001";
    public static final String LOOKUP_TYPE_6699002 = "6699002";
    public static final String LOOKUP_TYPE_6699003 = "6699003";
    public static final String LOOKUP_TYPE_6699004 = "6699004";
    public static final String LOOKUP_TYPE_6699005 = "6699005";
    public static final String LOOKUP_TYPE_6672 = "6672";
    public static final String LOOKUP_TYPE_6672009 = "6672009";

    public static final String LOOKUP_TYPE_8006008 = "8006008";
    public static final String LOOKUP_TYPE_800600800007 = "800600800007";
    public static final String LOOKUP_TYPE_800600800001 = "800600800001";
    public static final String LOOKUP_TYPE_800600800002 = "800600800002";
    public static final String LOOKUP_TYPE_800600800003 = "800600800003";
    public static final String MAILBOX_SUFFIX = "@zte.com.cn";
    public static final String SEMICOLON = ";";
    public static final String COLON = ";";

    public static final String NEWLINE = "\n";
    public static final String SUBJECT_182 = "182装配数据推送PDM系统异常，请注意!";

    public static final String ZTE_CN_NAME = "中兴通讯股份有限公司";
	public static final String SZPROD_FULL_NAME = "终端深圳生产部/制造部/供应链/中兴通讯股份有限公司";
    public static final String CONTENT_182 = "物料代码:%s 版本:%s 推送次数%s <br>";
    public static final String ENABLE_FLAG_Y = "Y";
    public static final long NINE_CREATEBY = 999;
    public static final String CREATE_NAME_BY = "9999";
    public static final long INIT_DIGTAL = 0;
    public static final String PROCESS_STATUS_STRING = "待推送";
    public static final String CONST_BO_STRING = "bo";

    public static final String GBOMCSGINFOS = "gbomCsgInfos";

    public static final String CUSTOMIZED = "CUSTOMIZED";
    public static final String CONST_CODE_STRING = "code";
    public static final String CONST_EMPTY_STRING = "";
    public static final long CONST_DIGTAL_DATA = 1;
    public static final String PROCESS_RESULT = "推送失败";
    public static final String PROCESS_SUCESS_STRING = "已经推送";
    public static final String PROCESS_RESULT_SUCESS_DT = "推送成功";
    public static final String CONST_STOCKRETURN = "退库";
    public static final String HAVEING = "有";
    public static final String SOURCE_SYSTEM = "MES";
    public static final String THREE_ZERO_STRING = "000";
    public static final String STRING_001 = "001";
    public static final String STRING_002 = "002";
    public static final String STRING_003 = "003";
    public static final String STRING_004 = "004";
    public static final String STRING_005 = "005";
    public static final String STRING_006 = "006";
    public static final String STRING_007 = "007";
    public static final String STRING_15 = "15";
    public static final String FLAG_S_D = "S";
    public static final String FLAG_N_D = "F";
    public static final String MARK_FLAG = "mark";
    public static final String NOT_HAVE = "没有";
    public static final String LOOKUP_TYPE_6610 = "6610";
    public static final String UNDER_LINE = "_";
    public static final String SYNC_ERROR = "来料信息同步失败";
    public static final long TIME_THIRTHY_MINUTE = 1800000;
    public static final String LOOKUP_TYPE_8882 = "8882";
    public static final String LOOKUP_TYPE_8882001 = "8882001";
    public static final String LOOKUP_TYPE_8882002 = "8882002";
    public static final String CMS_LOOK_UP_TYPE = "6301";
    public static final int FIVE_NUMBER_DIGITAL = 5;
    public static final String REGEX_MATCH = "^[+-]?\\d*$";
    public static final int ZERO_NUMBER_NAME = 0;
    public static final int ONE_NUMBER_NAME = 1;
    public static final String KEY_2060029 = "2060029";
    public static final String KEY_2027015 = "2027015";
    public static final String FORM_NAME_DT = "forName";
    public static final String ORG_DT_NAME = "orgDtList";
    public static final String KEY_TYPE = "keyType";
    public static final String LOOKUP_TYPE_7192 = "7192";
    public static final String LOOKUP_TYPE_7192001 = "7192001";
    public static final String LOOKUP_TYPE_7192002 = "7192002";
    public static final String LOOKUP_TYPE_7192003 = "7192003";

    public static final String LOOKUP_TYPE_241127 = "241127";
    public static final String LOOKUP_TYPE_241127001 = "241127001";
    public static final String LOOKUP_TYPE_241127002 = "241127002";

    public static final String LOOKUP_TYPE_250320 = "250320";
    public static final String LOOKUP_TYPE_250320001 = "250320001";
    public static final String LOOKUP_TYPE_250320002 = "250320002";
    public static final String BARCODE_ZH_CN = "zh_CN";
    public static final String IMES_300_DH = "imes300";
    public static final String EMPLOYEE_10312837 = "10312837";
    public static final String EMPLOYEE_10229661 = "10229661";
    public static final String EMPLOYEE_10001 = "10001";
    public static final String MES_BARCODE_DT = "MES_BARCODE_DT";
    public static final int INT_41_NUMER = 41;
    public static final String ITEM_BARCODE_NAME_DT = "IBARCODE";
    public static final String SERIAL_NUMBER_BARCODE = "serial_number_barcode";
    public static final String SEQUENCE_NAME_DT = "序列码";
    public static final String BATCH_CODE = "批次码";
    public static final String LOOKUP_TYPE_1199 = "1199";

    public static final String LOOKUP_TYPE_3000 = "3000";
    public static final String LOOKUP_TYPE_1199001 = "1199001";
    public static final String LOOKUP_TYPE_1199002 = "1199002";

    public static final String BAIND_MAIN_BARCODE = "请绑定主条码，请确认";
    public static final String TOKEN_ERR = "token验证失败";

    public static final String SITE_OR_ORGID_ERR_NO_FIND_CONFIGINFO = "输入站点或者组织错误或者配置id错误，无法查询到配置信息，请确认";
    public static final String SUBCODE_CONFIGID_MUST_THEREE_OR_FOUR = "输入子配置ID必须是3层或4层物料，请确认";
    public static final String SEQNUM = "序列码";
    public static final String SN_CODE = "SN_CODE";
    public static final String ENTITY_ERR_NO_FIND_ENTITYID = "输入任务号错误，未找到任务ID，请确认";
    public static final String BATCH_CODE_S = "BATCH_CODE";
    public static final String SUBCODE_IS_SEQ_CONTROL = "该子条码是序列号控制，请扫描序列号!";
    public static final String MAINCODE_CONFIGID_MUST_THEREE_OR_FOUR = "输入主配置ID必须是3层或4层物料，请确认";
    public static final String MAINCODE_ENVI_ATTRIBUTE_ERR = "主条码环保属性校验不通过，请确认";


    public static final String SUBCODE_ENVI_ATTRIBUTE_ERR = "子条码环保属性校验不通过，请确认";
    public static final String FIRST_BAIND_MAINCODE_THEN_SCAN_SUBCODE = "主条码条码必须先绑定才能扫描子条码，请确认";
    public static final String SUBCODE_CATEGORY_CONFIGID_MUST_INPUT = "子条码标识必须输入，请确认";
    public static final String SUBCODE_MUST_OVER_ZERO = "子条码扫描数量必须大于0，请确认";
    public static final String SUBITEMCODE_CATEGORY_MUST_INPUT = "子物料代码,子条码条码大类必须输入，请确认";
    public static final String SUBCATEGORY_NO_FIND = "子条码条码大类未找到，请确认";
    public static final String SUBITEMCODE_NO_FIND = "子条码对应的物料代码未找到，请确认";
    public static final String SUBBARCODE_BATCHCODE_IS_NULL = "子条码220批次码不能为空，请确认";
    public static final String SUBCODE_MAINCODE_IS_SAMPLE = "主条码和子条码不能相同，请确认";
    public static final String SITEID_MAINCODE_MAINCODE_CATEGORY_ORGID_SEQ_NULL = "员工工号、主条码、主条码标识、组织ID、不关联任务号必须输入，请确认";
    public static final String ENTITYID_SITEID_IS_NULL = "没有勾选不关联任务号，任务号、站点ID必须输入，请确认";
    public static final String MAINCODE_MAINCODE_CATEFORY_IS_NULL = "主物料代码,主条码条码大类必须输入，请确认";
    public static final String MAINCODE_CATEGORY_MUST_SQENUM = "主条码条码大类只能是序列码，请确认";

    public static final String MAINCODE_CATEGORY_ONLY_SQENUM = "主条码条码大类只能是序列码或者未找到，请确认";


    public static final String MAINCODE_NO_FIND = "主物料代码未找到，请确认";
    public static final String SUBBARCODE_IS_EMPTY = "子条码为空，校验结束";
    public static final String REEL_MP_CODE = "REEL_MP_CODE";
    public static final String CONTAINER_CODE = "CONTAINER_CODE";
    public static final String PARENY_ID_DT = "parentId";
    public static final int INT_5 = 5;
    public static final String SUB_BARCODE_TYPE = "subBarcodeType";
    public static final String SUB_LIST_BARCODE_INFO = "subListBarcodeInfo";
    public static final String MAIN_CPM_CONFIG_ITEM_ASSEMBLE = "mainCpmConfigItemAssemble";
    public static final String LAST_COUNT = "lastCount";
    public static final String LAST_ENITY_ID = "lastEnityId";
    public static final String LAST_CONFIG_ID = "lastConfigId";
    public static final String LAST_NODE_COUNT = "lastNodeCount";
    public static final String ENITY_NAME = "enityName";
    public static final String RECORD_ID = "recordId";
    public static final String PARENT_ID = "parentId";
    public static final String MAIN_CPM_CONFIG_ITEM_ASSEMBLE_X = "mainCpmConfigItemAssemble";
    public static final String LOOKUP_TYPE_1189 = "1189";
    public static final String LOOKUP_TYPE_1179 = "1179";
    public static final String EMP_NO_ERROR = "工号验证失败";
    public static final String BOX_LIST_LARGE = "箱列表不能超过：";
    public static final String PAGE_SIZE_LARGE = "页码不能超过：";
    public static final String PAGE_BIG_ZERO = "页码或者当前页大小必须大于0";
    public static final String LOOKUP_TYPE_1169 = "1169";


    public static final String MAINCODE_CONFIGID_SEQ_IS_NULL = "关联任务号:工序，主条码配置ID必须输入，请确认";
    public static final String SUBCODE_CONFIGID_IS_NULL = "关联任务号:子条码配置ID必须输入，请确认";

    public static final String PALLET_NO = "PALLETNO";
    public static final String SCAN_OVER = "正常结束";
    public static final int EIGHT_8 = 8;
    public static final String BILL_NUMBER_DT = "BILLNUMBER";
    public static final String CA_DT_21 = "21CA";

    public static final String MES = "MES";

    public static final String MEITUAN = "MEITUAN";

    public static final String DELETE_OLD_DAILY_REPORT = "DELETE_OLD_DAILY_REPORT";

    public static final String PRE_BOM_REDIS_KEY = "preBomRedisKey:%s";
    public static final String PRE_ITEM_REDIS_KEY = "preItemRedisKey:%s";
    public static final String LOCK = "lock:";
    public static final String BAR_SUBMIT_STR = "bar_submit_info";
    public static final String BAR_SUBMIT_EXPORT_XLSX = "入库单信息.xlsx";
    public static final String BAR_SUBMIT_EXPORT = "入库单信息";
    public static final String EMAIL_PREFIX = "<p style=\"font-family:arial;font-size:30px;\">";
    public static final String EMAIL_PREFIX_A = "<a style=\"color:blue;font-size:25px;font-weight:bold;\" href=";
    public static final String EMAIL_SUFFIX = "</a></p>";
    public static final String EMAIL_SUFFIX_P = "</p>";
    public static final String EMAIL_COLO = ">";
    public static final String EMAIL_SPLIT = "_";
    public static final String CLICK_DOWN = "请点击下载";
    public static final BigDecimal TIME_INTERVAL = (new BigDecimal("180"));
    public static final long DAY_TIME = 1000 * 60 * 60 * 24;

    public static final String MES_MACHINE_INFORMATION_FEEDBACK = "MES整机信息回传错误";

    public static final String LENGTH_OR_WIDTH_OR_HIGN_IS_ZERO_SUBJECT = "你好，内文任务箱号尺寸数据异常为【零】，请关注处理，谢谢。";

    public static final String FAILED_GET_MATERIAL_FOR_TASK = "获取任务装配物料代码信息失败";

    public static final String FAILED_GET_COMPLETION_TIME_OF_INBOUND_BOOKKEEPING = "获取任务入库记账完成时间失败";

    public static final String FAILED_GET_ASSEMBLY_AND_PACKAGING_TIME = "获取任务:%s装配包装时间失败";

    public static final String FAILED_GET_MATERIAL_CUSTOMER_INFO = "获取任务装配物料代码对应客户基础信息失败";

    public static final String BYTE_DANCE_UNIT_NAME = "北京字跳网络技术有限公司";
    public static final String MEI_TUAN_UNIT_NAME = "北京三快在线科技有限公司";
    public static final String ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE = "ZTEiMES-ByteDance-ServerQualityCode";
    public static final String ZTEIMES_MEI_TUAN_QUALITYCODE = "ZTEiMES-Meituan-qualityCode";
    public static final String ZTEIMES_ALIBABA_QUALITYCODE = "ZTEiMES-Alibaba-QualityGenerateQualitycode";

    public static final String SOLT_IS_EMPTY_ELSE_IT = "不涉及槽位信息";

    public static final String NOT_INVOLVED_NOT_GET = "不涉及/无法获取";

    public static final String FAILED_GET_MATERIAL_THE_SERVER_CUSTOMER_INFO = "获取任务装配物料代码的服务器sn失败";

    public static final String FAILED_GET_MATERIAL_MORE_THAN_ONE_THE_SERVER_CUSTOMER_INFO = "任务装配物料代码对应多个服务器sn";

    public static final String FAILED_TO_OBTAIN_SERVER_SN_MODEL = "获取服务器sn机型失败";

    public static final String FAILED_GET_MATERIAL_FOR_SERVICE_SN = "获取服务器sn装配物料代码信息失败";

    public static final String FAILED_GET_ASSEMBLE_MATERIAL_FOR_TASK = "获取任务组装物料信息失败";

    public static final String FAILED_GET_MATERIAL_FOR_SERVICE_SN_CUSTOMER_INFO = "获取服务器sn装配物料对应客户基础信息失败";

    public static final String FAILED_TO_OBTAIN_TASK_PO_NUMBER = "获取任务对应客户PO号失败";

    public static final String FAILED_TO_NO_PO_PRODUCT = "无PO提前生产";

    public static final String FAILED_GET_MATERIAL_ZB_CUSTOMER_INFO = "获取任务装配物料代码的主板sn失败";

    public static final String FAILED_GET_MATERIAL_MORE_THAN_ONE_ZB_CUSTOMER_INFO = "任务装配物料代码对应多个主板sn";

    public static final String ASSEMBLING = "装配";
    public static final String PACKING = "包装";

    public static final String MDS_OBTAINS_TOKEN_KEY = "imes_zte-mes-manufactureshare-datawbsys_" + "mds_obtains_token_key_";

    public static final String LOOKUP_TYPE_8240057 = "8240057";
    public static final String LOOKUP_TYPE_6732 = "6732";
    public static final String LOOKUP_TYPE_6732001 = "6732001";
    public static final String LOOKUP_TYPE_6732002 = "6732002";
    public static final String LOOKUP_TYPE_6732003 = "6732003";
    public static final String LOOKUP_TYPE_6732004 = "6732004";
    public static final String LOOKUP_TYPE_6732005 = "6732005";
    public static final String LOOKUP_TYPE_6732006 = "6732006";
    public static final String LOOKUP_TYPE_6732007 = "6732007";

    public static final String USER_NAME = "username";
    public static final String PASS_WORD = "password";
    public static final String GRANT_TYPE = "grant_type";
    public static final String Z_ACCESS_TOKEN = "Z-ACCESS-TOKEN";

    public static final String PARTCODELIST = "partcodeList";

    public static final String PARTCODE = "partcode";

    public static final String STATIONIDLIST = "station_id_list";

    //条码中心接口地址
    public static final String LOOKUP_6679 = "6679";
    //根据目录名称获取模板列表
    public static final String LOOKUP_6679001 = "6679001";
    //条码中心适配程序下载地址获取接口
    public static final String LOOKUP_6679002 = "6679002";
    //条码中心打印接口
    public static final String LOOKUP_6679003 = "6679003";
    //根据模板名称获取模板信息
    public static final String LOOKUP_6679004 = "6679004";
    //条码扩展信息查询
    public static final String LOOKUP_6679005 = "6679005";

    // 条码中心地址
    public static final String MP_CODE_KEY_1004052 = "1004052";
    public static final int BARCODE_BASIC_URL = 1004052001;
    public static final int BARCODE_TENANT_ID = 1004052002;
    public static final int BARCODE_WHITE_EMP = 1004052003;
    public static final int BARCODE_UPDATE_URI = 1004052005;
    public static final int BARCODE_EXPAND_QUERY_URI = 1004052006;
    public static final int BARCODE_QUERY_URI = 1004052007;
    public static final int BARCODE_REGISTER_UIR = 1004052008;
    public static final int BARCODE_GENERATE_UIR = 1004052009;
    public static final int BARCODE_BLANK_GENERATE_URI = 1004052010;
    public static final int BARCODE_LOOKUPCODE = 1004052012;
    public static final int BARCODE_APPID = 1004052011;

    public static final String X_AUTH_ACCESSKEY = "X-Auth-AccessKey";
    /* Started by AICoder, pid:i3b00w6ce7cba2a14f4c0abf50afa00d40a309a7 */
    public static final String APP_CODE = "a767e44788a04041b60d49c22d099d99";

    public static final String APP_EN_NAME = "iMES-Web";

    public static final String VENDOR_NAME = "ZXISCP_iMES300";

    /* Ended by AICoder, pid:i3b00w6ce7cba2a14f4c0abf50afa00d40a309a7 */

    public static final String HEADER = "header";
    /* Started by AICoder, pid:752b9q86e3t0bd5142140acf30f5171da080dddd */
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN = "2030099";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_MDS = "2030098";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_ORGID = "203009900001";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_USRADD = "203009900002";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_ENAME = "203009900003";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_STAT = "203009900004";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_BOQ = "203009900005";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_00006 = "203009900006";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_00007 = "203009900007";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_00008 = "203009900008";
    public static final String ZTEIMES_BAIDU_SERVER = "ZTEiMES-Baidu-ProductionOrder";
    public static final String BAIDU = "Baidu";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_BAIDU = "2030101";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_BAIDU_USRADD = "203010100002";
    public static final String LOOKUP_TYPE_OVERALL_UNIT_BAIDU_ENAME = "203010100003";
    public static final String LOOKUP_TYPE_8240039 = "8240039";
    public static final String LOOKUP_TYPE_8240040 = "8240040";
    public static final String LOOKUP_TYPE_8240041 = "8240041";
    public static final String LOOKUP_TYPE_8240042 = "8240042";
    public static final String LOOKUP_TYPE_8240043 = "8240043";
    public static final String LOOKUP_TYPE_8240044 = "8240044";
    public static final String LOOKUP_TYPE_8240045 = "8240045";
    public static final String MESSAGE_TYPE_B2B = "ZTEiMES-ByteDance-Server";

    public static final String MESSAGE_TYPE_B2BFEEDBACK = "ZTEiMES-ByteDance-MOCompletionFeedback";

    public static final String MESSAGE_TYPE_B2BSET = "ZTEiMES-ByteDance-ServerCollectionData";

    public static final String LOOKUP_TYPES_8240042 = "8240042";
    public static final String LOOKUP_TYPE_824004200001 = "824004200001";
    public static final String LOOKUP_TYPE_824004200002 = "824004200002";
    public static final String LOOKUP_TYPE_824004200003 = "824004200003";
    public static final String LOOKUP_TYPE_824004200004 = "824004200004";
    public static final String LOOKUP_TYPE_824004200005 = "824004200005";
    public static final String LOOKUP_TYPE_824004200010 = "824004200010";
    public static final String LOOKUP_TYPE_824004200006 = "824004200006";
    public static final String LOOKUP_TYPE_824004200011 = "824004200011";

    public static final String LOOKUP_TYPE_824004200012 = "824004200012";
    public static final String LOOKUP_TYPE_824004200020 = "824004200020";
    public static final String LOOKUP_TYPE_824004200021 = "824004200021";
    public static final String LOOKUP_TYPE_824004200022 = "824004200022";
    public static final String LOOKUP_TYPE_824004200023 = "824004200023";

    public static final String LOOKUP_TYPE_824004200025 = "824004200025";

    public static final String LOOKUP_TYPE_824004200026 = "824004200026";
    public static final String LOOKUP_TYPE_824004200027 = "824004200027";
    public static final String LOOKUP_TYPE_824004200028 = "824004200028";
    public static final String LOOKUP_TYPE_824004200029 = "824004200029";

    public static final String LOOKUP_TYPE_824004200030 = "824004200030";

    public static final List<String> STATION_INFO_ATTRIBUTE = Arrays.asList("气密测试工序", "注液测试工序", "烘干", "注氮");

    public static final String LOOKUP_TYPE_3020035 = "3020035";

    public static final String LOOKUP_TYPE_302003600001 = "302003600001";

    public static final String STRING_GE = "个";
    public static final String STRING_N003 = "N003";
    public static final String STRING_RECEIPT = "Receipt";
    public static final String LOOKUP_TYPE_824004200024 = "824004200024";
    public static final String PASSWORD_TOKEN = "password";
    public static final String STATION_LOG_BYTEDANCE = "ZTEiMES-ByteDance-ServerStationLog";
    public static final String STATION_INFO_BYTEDANCE = "ZTEiMES-ByteDance-ServerStation";
    public static final String ZTEIMES_MEITUAN_SERVER = "ZTEiMES-Meituan-CreateProduce";
    public static final String MESSAGE_TYPE_B2B_MEITUAN = "ZTEiMES-Meituan-ProduceDateUpload";
    public static final String MESSAGE_TYPE_B2B_BYTEDANCEMR = "ZTEiMES-ByteDance-GoodsIssueOfProductionOrder";

    public static final String MESSAGE_TYPE_B2B_BYTEDANCEPO = "ZTEiMES-ByteDance-ProductionOrder";

    public static final String MESSAGE_TYPE_B2B_MEITUAN_ERRORDETAILUPLOAD = "ZTEiMES-Meituan-ErrorDetailUpload";
    public static final String MEITUAN_TEST_FILE_BYTEDANCE = "ZTEiMES-Meituan-TestFileUpload";

    public static final String MESSAGE_TYPE_TEST_FILE_ALIBABA = "ZTEiMES-Alibaba-QualitycodeFile";

    public static final String MESSAGE_TYPE_B2B_TENCENTMPT = "ZTEiMes-Tencent-PushMPTTestData";
    public static final String MESSAGE_TYPE_B2B_TENCENTMACHINELOG = "ZTEiMes-Tencent-PushBoardTestLog";
    public static final int INT_51 = 51;
    public static final String PN = "PN";
    public static final String PRE_PRODUCTION_WITHOUT_PO = "无PO提前生产";
    public static final String ZTE = "ZTE-";
    public static final String PART_CODE_LIST = "partcodeList";
    public static final String PART_CODE = "partcode";

    public static final String PROJECT_NAME = "生产订单完工反馈";

    public static final String PROJECTSET_NAME = "生产部件信息采集";

    public static final String SN = "sn";

    public static final String SLOT_NAME = "不涉及槽位信息";

    public static final String STATION_ID_LIST = "station_id_list";

    public static final String STATION_ID = "station_id";

    public static final String MTHC = "MTHC";

    public static final String FILE_CLOUD_LINK_SWITCH = "file_cloud_link_switch";
    public static final String FAIL = "FAIL";
    public static final String STR_OTHER = "其他";
    public static final String STR_NINE = "9";

    public static final String SCAN_MSG = "扫描数量大于配置数量(";

    public static final String MAIN_BOX = "主箱";
    public static final String SUB_BOX = "辅箱";

    public static final String MUST = "必配";

    public static final Pattern P = Pattern.compile("[^0-9]");
    public static final Pattern NUMBER_LETTER_UNDERLINE = Pattern.compile("^[a-zA-Z0-9_]+$");
    // 推送组装关系到MES详表标识
    public static final String FLAG_IMES = "iMES";

    public static final String PROJECT_PHASE_WAREHOUSE = "WAREHOUSE";
    public static final String PROJECT_PHASE_PROTOTYPE = "PROTOTYPE";

    public static final String PROJECT_PHASE_WEIXIN = "WEIXIN";

    // B2B回调返回结果
    public static final String PUSH_B2B_STATUS_CY = "CY";
    public static final String PUSH_B2B_STATUS_CN = "CN";
    // 数据上传状态
    public static final String UPLOAD_SUCCESS = "上传成功";
    public static final String UPLOAD_FAILURE = "上传失败";
    // 字段名称
    public static final String STATUS_FIELD = "status";
    public static final String BILL_NO_FIELD = "billNo";
    public static final String HEADER_ID_FIELD = "headerId";

    public static final String STRING_X = "X";
    public static final String STRING_Y = "Y";
    public static final String STRING_FALSE = "false";
    public static final String SCATTER = "scatter";
    public static final String GENERAL = "general";
    public static final String BATCH = "batch";

    public static final String MES_MESSAGE = "MES返回信息：";
    public static final String BYTE_DANCE = "ByteDance";
    public static final String INDICATOR_ZH = "生产液冷数据";
    public static final String MATERIAL_REQUISITION_ZH = "生产订单发料";
    public static final String ENTITY_ZH = "生产订单";
    public static final String ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR = "ZTEiMES-ByteDance-UploadIndicator";
    public static final String ZTE_IMES_BYTEDANCE_INVENTORY = "ZTEiMES-ByteDance-Inventory";
    public static final String ZTEiMES_BYTEDANCE_GOODRECEIPSOFMO = "ZTEiMES-ByteDance-GoodReceiptsOfMO";
    public static final String ZTE_PROD_ORDER = "生产订单收货";
    public static final String DEVICE_INVENTORY_ZH = "整机库存";
    public static final String DEVICE_INVENTORY_MOVE_ZH = "整机库存移动";
    public static final String DEVICE_WHOLE_INVENTORY_MOVE_ZH = "整机车间库库存移动";
    public static final String ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT = "ZTEiMES-ByteDance-InventoryMovement";

    public static final String ZTE_IMES_TENCENT_PUSHTESTDATA = "ZTEiMes-Tencent-PushTestData";
    public static final String MES_BATCHNO = "ZTE1";

    public static final String CBOM_EXTENTION_ZH_CODE = "整机编码";
    public static final String CBOM_EXTENTION_ZH_NAME = "整机名称";
    public static final String LOOKUP_TYPES_8240059 = "8240059";
    public static final String LOOKUP_TYPE_824005900001 = "824005900001";
    public static final String LOOKUP_TYPE_824005900002 = "824005900002";
    public static final String LOOKUP_TYPE_824005900003 = "824005900003";
    public static final String LOOKUP_TYPE_824005900004 = "824005900004";
    public static final String LOOKUP_TYPE_824005900005 = "824005900005";
    public static final String LOOKUP_TYPE_824005900006 = "824005900006";
    public static final String LOOKUP_TYPE_824005900007 = "824005900007";
    public static final String LOOKUP_TYPE_824005900008 = "824005900008";
    public static final String LOOKUP_TYPES_8240060 = "8240060";

    public static final String LOOKUP_TYPES_3020041 = "3020041";
    public static final String CUSTOMER_ID = "customerId";
    public static final String CBOM_NAMES = "cbomNames";
    public static final String T = "T";
    public static final String F = "F";
    public static final String STR_ZTE = "ZTE";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";
    // 子库存数据字典
    public static final String LOOKUP_TYPE_202408291 = "202408291";
    // 运行时间点数据字典
    public static final String LOOKUP_TYPE_202408292 = "202408292";
    public static final String LOOKUP_TYPE_202408292001 = "202408292001";
    public static final String ZJ_NANJING = "ZJ-NANJING";
    public static final String MES_FG01 = "FG01";
    public static final String MES_FG02 = "FG02";
    public static final String MES_EA = "EA";
    /* Started by AICoder, pid:0679b78093f489614f1f095460ced71c2a226c88 */
    public interface ByteDance {
        String MOVE_TYPE_BD05 = "BD05";
        String MOVE_TYPE_BD15 = "BD15";
        String MOVE_TYPE_BD53 = "BD53";
        String MOVE_TYPE_BD54 = "BD54";
        String MOVE_TYPE_BD66 = "BD66";
        String MOVE_TYPE_BD67 = "BD67";
        String OD_MOVE_TYPE_RECEIPT = "Receipt";
        String OD_MOVE_TYPE_ISSUE = "Issue";
        String MOVE_TYPE_BD21 = "BD21";
        String MOVE_TYPE_BD22 = "BD22";
    }
    /* Ended by AICoder, pid:0679b78093f489614f1f095460ced71c2a226c88 */
    public static final String OUTSTANDING_SALE_UPLOAD = "销售订单数据上传";
    public static final String MES_GL_GT_UPLOAD = "MES领退料单据上传";
    public static final String ZTE_IMES_BYTEDANCE_SALES_ORDER = "ZTEiMES-ByteDance-SalesOrder";
    public static final String REISSUED = "补发(产品)";
    public static final String EN_Q = "Q";
    public static final String EN_H = "H";
    public static final String EQUIPMENT_SUPPLY = "设备供货";

    public static final String LOOKUP_TYPE_FORWARD_TENCENT = "2030102";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_ORGID = "203010200001";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_USRADD = "203010200002";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_ENAME = "203010200003";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_STAT = "203010200004";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_BOQ = "203010200005";
    public static final String LOOKUP_TYPE_203010200009 = "203010200009";
    public static final String LOOKUP_TYPE_203010200010 = "203010200010";

    public static final String LOOKUP_TYPE_824006300013 = "824006300013";

    public static final String LOOKUP_TYPE_824006300014 = "824006300014";
    public static final String LOOKUP_TYPE_824006300015 = "824006300015";

    public static final String LOOKUP_TYPE_8240063 = "8240063";

    public static final String LOOKUP_TYPE_824009200004 = "824009200004";

    public static final String LOOKUP_TYPE_8240092 = "8240092";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_CUSTOMER_ID = "203010200006";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_CPQD_URL = "203010200007";
    public static final String LOOKUP_TYPE_FORWARD_TENCENT_INTERCEPT = "203010200008";
    public static final String ZH_ZHU_BAN = "主板";
    public static final String ZH_ZHENG_JI = "整机";
    public static final String TASK_ID = "task_id";

    public static final String TENCENT = "Tencent";
    public static final String TENCENT_ZH = "腾讯";
    public static final String MEITUAN_ZH = "美团";
    public static final String ALIBABA = "alibaba";
    public static final String SUPPLIER_WRITE_SERVER_PART_INFO = "SupplierWriteServerPartInfo";
    public static final String RUN = "run";
    public static final String TENCENT_FORWARD_ZH = "正向数据上传";
    public static final String ZTE_IMES_TENCENT_FORWARD_UPLOAD = "ZTEiMes-Tencent-ForwardUpload";
	public static final String LOOKUP_TYPE_824009200001 = "824009200001";
	public static final String LOOKUP_TYPE_824009200002 = "824009200002";
	public static final String LOOKUP_TYPE_824009200005 = "824009200005";
	public static final String LOOKUP_TYPE_824009200011 = "824009200011";
	public static final String LOOKUP_TYPE_824009200012 = "824009200012";
	public static final String LOOKUP_TYPE_824009200013 = "824009200013";

    /**
     * 阿里测试日志
     */
    public static final String LOOKUP_TYPE_824009200014 = "824009200014";
    public static final String ZTE_IMES_TENCENT_FORWARD_QUERY = "ZTEiMes-Tencent-ForwardQuery";
    public static final String ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA = "ZTEiMes-Tencent-QueryMPTTestData";

    public static final String TYPE_MPT = "MPT";
    public static final String MPT_ACTION_DATA = "SupplierUploadLog";
    public static final String MPT_LOG_TYPE = "LogType";
    public static final String MPT_ACTION = "Action";
    public static final String MPT_START_COMPANY = "StartCompany";
    public static final String MPT_START_COMPANY_DATA = "ZTE";
    public static final String MPT_ORDER_NO = "OrderNo";
    public static final String MPT_SERVER_SN = "ServerSN";
    public static final String MPT_STR_MODEL_NAME = "StrModelName";
    public static final String MPT_STR_DEVICE_CLASS_NAME = "StrDeviceClassName";
    public static final String MPT_STR_VERSION = "StrVersion";
    public static final String MPT_FILE = "File";
    public static final String MPT_VERSION_ID = "VersionId";
    public static final String LOOKUP_CODE_MPT_DATA_TYPE = "824009200002";
    public static final String LOOKUP_CODE_MACHINE_TEST_LOG_DATA_TYPE = "824009200003";
    public static final String LOOKUP_CODE_824009200004 = "824009200004";

    public interface tencentUpload {
        String NO_CORRESPONDING_TENCENT_MODEL_FOUND = "未找到对应腾讯机型";
        String PASS = "PASS";
        String P = "P";
        String F = "F";
        String TENCENT = "Tencent";

        String WHOLE_MACHINE_TESTING_DATA = "wholeMachineTestingData";

        String RUN_IN = "run-in";

    }

    public static final String INITIALIZATION_SERVICE_FAILED = "初始化WholeMachineUpTestRecordService失败";

    public static final List<String> MACHINE_PHASE = Arrays.asList("0", "2", "3", "6");
    public static final List<String> MPT_PHASE = Arrays.asList("1", "4");


    public static final String LOOKUP_TYPE_8001040 = "8001040";
    public static final String LOOKUP_TYPE_8001050 = "8001050";
    /**
     * OEM字段表类型
     */
    public static final String LOOKUP_TYPE_8001032 = "8001032";
    /**
     * CPE 字段表类型
     */
    public static final String LOOKUP_TYPE_8001033 = "8001033";
    public static final String D_MAC = ",MAC";
    public static final String MAC = "MAC";
    public static final String OEM = "OEM";
    public static final String CPE = "机顶盒";
    public static final String M_VALUE = "-M";
    public static final String ZH_CPU = "CPU";
    public static final String ZH_GPU = "GPU";

    public static final String ZH_CABLE = "Cable";
    public static final String ZH_FS = "风扇";
    public static final String ZH_ZK = "子卡";
    public static final String ZH_NC = "内存";

    public static final String ZH_BB = "背板";
    /**
     * 字符串 #
     */
    public static final String HASH = "#";

    public static final String LOOKUP_TYPE_FOR_ALIBABA = "2030104";
    public static final String LOOKUP_CODE_203010400001 = "203010400001";
    public static final String LOOKUP_CODE_203010400002 = "203010400002";
    public static final String LOOKUP_CODE_203010400003 = "203010400003";
    public static final String LOOKUP_CODE_203010400004 = "203010400004";
    public static final String LOOKUP_CODE_203010400005 = "203010400005";
    public static final String LOOKUP_CODE_203010400006 = "203010400006";

    public static final String LOOKUP_CODE_203010400007 = "203010400007";
    public static final String LOOKUP_TYPE_8240067 = "8240067";
    public static final String LOOKUP_CODE_824006300016 = "824006300016";
    public static final String LOOKUP_CODE_824006300018 = "824006300018";
    public static final String LOOKUP_CODE_824006300019 = "824006300019";
    public static final String ZTE_NJ_ZN_COMPANY = "ZTE-中兴南京智能技术有限公司";
    public static final String ZTE_IMES_ALIBABA_AIS_META_MCT = "ZTEiMES-Alibaba-MctServerproductinfo";
    public static final String RIGHT_BRACE = "}";

    public static final String ZMS_ALI_EMAIL_SUBJECT = "阿里领退料异常单据告警！";
    public static final String ZMS_ALI_BACK_ERROR = "阿里领退料回调结果为空：\n";
    public static final String ZMS_ALI_EMAIL_CONTENT = "以下单据多次上传失败，请处理：\n";
    public static final String ZMS_TENCENT_QL_ERROR = "腾讯质量码返回错误，具体原因请联系腾讯";
    public static final String ZMS_TENCENT_API_EMPTY = "比对结果返回空值！\n";
    public static final String ZMS_TENCENT_NOT_UPLOAD = "未上传！";

    public static final String PQ_REGULAR ="[PQ]";

    public static final String LOOKUP_TYPE_2030107 = "2030107";

    public static final String CONTRACT_NUMBER = "contractNumber";

    public static final String CALL_ICMS_ERROR = "调icms获取需要上传合同号失败:";

    /**
     * 机框模组物料代码数据字典
     */
    public static final String LOOKUP_TYPE_MODULE_ITEM_CODE = "8240038";


}
