/**
 * 项目名称 : quping20181217
 * 创建日期 : 2018-12-17
 * 修改历史 :
 *   1. [2018-12-17] 创建文件 by 10095344
 **/
package com.zte.application.datawb;

import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLines;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.interfaces.dto.WsmAssembleLinesEntityDTO;
import com.zte.interfaces.dto.WsmAssembleLinesEntityWithNameDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;
import java.util.Map;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public interface WsmAssembleLinesService {

	/**
	 * 
	 * @param line
	 * @return
	 */
    List<WsmAssembleLines> selectMainBarcodeBySubBarcode(WsmAssembleLines line);

	/**
	 * @param dto
	 * @return WsmAssembleLinesDTO
	 * <AUTHOR> 袁海洋
	 */
	ServiceData getWsmAssembleLinesDTOPage(WsmAssembleLinesDTO dto);
	/**
	 * @param dto
	 * @return WsmAssembleLinesDTO
	 * <AUTHOR> 袁海洋
	 */
	ServiceData getWsmAssembleLinesDTOCount(WsmAssembleLinesDTO dto);

	List<WsmAssembleLinesEntityDTO> getAssemblyMaterials(String itemBarcode);
	List<WsmAssembleLinesEntityDTO> getAssemblyMaterialList(List<String> itemBarcodeList);
	List<WsmAssembleLinesEntityWithNameDTO> getAssemblyMaterialListWithEntityName(List<String> itemBarcodeList);

	List<SysLookupValues> getSysLookupValues(String lookUp);

	List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByEntityId(Integer entityId);

	String getCustomerPONumber(Integer entityId);

	/**
	 *根据主子条码查询现存绑定关系
	 */
	List<WsmAssembleLinesDTO> getWsmAssemblyListByMainAndSub(WsmAssembleLinesDTO paramDTO);


	public int deleteAssembleLineForRemoveRepeat();

}