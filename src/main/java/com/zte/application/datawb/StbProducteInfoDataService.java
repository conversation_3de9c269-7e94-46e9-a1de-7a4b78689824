package com.zte.application.datawb;

import com.zte.domain.model.datawb.StbProdDeliveryInfoQueryExtend;

import java.util.List;
import java.util.Map;

/**
 * STB_PRODUCTE_INFO 表查询Service
 * <AUTHOR>
 *
 */
public interface StbProducteInfoDataService {

	/**
	 * 机顶盒发货信息SFC.STB_PRODUCTE_INFO表字段查询
	 * @param barcodes
	 * @return
	 */
	List<StbProdDeliveryInfoQueryExtend> getListStbProdDeliveryInfo(String barcodes);

	Map<String, Integer> getDhomeSnListCount(List<String> snStartList, Integer daysAgoStart, Integer daysAgoEnd);

    /**
     * 根据 SN特征值 查询指定日期内 DHOME 发货的SN集合
     *
     * @param snStart
     * @param daysAgoStart
     * @param daysAgoEnd
     * @return
     */
    List<String> getDhomeSnListBySnStart(String snStart, Integer daysAgoStart, Integer daysAgoEnd);
}
