package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zte.application.datawb.MeiTuanService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.MeiTuanRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 美团服务类
 *
 * <AUTHOR>
 */
@Service
@DataSource(DatabaseType.SFC)
public class MeiTuanServiceImpl implements MeiTuanService {
    /* Started by AICoder, pid:d4df2gcba6h00d514b220942922a586d7884dd78 */

    @Autowired
    MeiTuanRepository meiTuanRepository;


    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private ZmsOverallUnitServiceImpl zmsOverallUnitService;


    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;

    /**
     * 理生产数据上传
     */
    @Override
    public ServiceData handleProductDataUpload(String empNo, MTProductDataUploadRequestDTO req) throws Exception {
        ServiceData ret = new ServiceData();
        try {
            //查询数据字典
            OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
            zmsOverallUnitService.getLookupMeanList(dto);

            //查询数据条件值
            OIDDto oidDto = new OIDDto();
            oidDto.setUserAddress(dto.getUserAddress());
            oidDto.setEntityName(dto.getEntityNameLike());
            List<String> oidList = new ArrayList<>();
            if (req != null && CollectionUtils.isNotEmpty(req.getOid())) {
                if (req.getOid().size() > Constant.INT_20) {
                    ret.setBo(Constant.OID_COUNT_GT20);
                    ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
                    return ret;
                }
                oidList = req.getOid();
            } else {
                //查询OID
                oidList = meiTuanRepository.getProductOID(oidDto);
            }

            List<EntityWeightDTO> entityWeights = mesGetDictInforRepository.getDict(Constant.LOOUP_TYPE_2030100);
            if (CollectionUtils.isNotEmpty(entityWeights)) {
                List<String> entityWeightsOID = entityWeights.stream().map(f -> f.getDescription()).collect(Collectors.toList());
                oidList.removeAll(entityWeightsOID);
            }

            if (CollectionUtils.isEmpty(oidList)) {
                ret.setBo(Constant.NO_DATA);
                ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                return ret;
            }
            oidDto.setOidList(oidList);
            DatabaseContextHolder.setDatabaseType(DatabaseType.WMES);
            List<MTProductDataUploadLogDTO> data = this.getProductDataUpload(oidDto);

            oidList = oidList.stream()
                    .map(s -> s.contains("_") ? s.substring(0, s.indexOf("_")) : s)
                    .collect(Collectors.toList());
            OIDDto oidDtoStatus = oidDto.clone();
            oidDtoStatus.setOidList(oidList);
            List<MtProductDataStatusDTO> dataStatus = this.getProductDataStatus(oidDtoStatus);

            if (CollectionUtils.isEmpty(dataStatus)) {
                dataStatus = new ArrayList<>();
            }
            this.pushToImesB2B(data, empNo, oidList, dataStatus);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        } catch (Exception ex) {
            ret.setBo(ex.getMessage());
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        }
        return ret;
    }

    /**
     * 获取生产数据
     *
     * @return
     */
    public List<MTProductDataUploadLogDTO> getProductDataUpload(OIDDto dto) {
        return meiTuanRepository.getProductDataUpload(dto);
    }

    /**
     * 查询美团状态
     *
     * @param dto
     * @return
     */
    public List<MtProductDataStatusDTO> getProductDataStatus(OIDDto dto) {
        return meiTuanRepository.getProductDataStatus(dto);
    }

    public void setDataProduceCount(List<MTProductDataUploadLogDTO> temp, String oid, List<MTProductDataUploadLogDTO> historyDTO) {
        if (CollectionUtils.isNotEmpty(historyDTO) && historyDTO.stream().anyMatch(n -> n.getItemCode().equals(oid))) {
            //查找temp集合中itemCode不存在history集合中的数据
            List<MTProductDataUploadLogDTO> his = historyDTO.stream().filter(m -> m.getItemCode().equals(oid)).collect(Collectors.toList());
            List<MTProductDataUploadLogDTO> notExistDTO = his.stream().filter(m -> temp.stream().noneMatch(n -> n.getProduceWoCode().equals(m.getProduceWoCode()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistDTO)) {
                if (notExistDTO.stream().anyMatch(n -> n.getProduceCount().equals(Constant.INT_1))) {
                    notExistDTO.forEach(item -> {
                        //设置成0在传美团
                        item.setProduceCount(Constant.ZERO_NUMBER_NAME);
                    });
                    temp.addAll(notExistDTO);
                }
            }
        }
    }

    public List<MTProductDataUploadLogDTO> setData(List<MTProductDataUploadLogDTO> data, String oid, List<MTProductDataUploadLogDTO> historyDTO) {
        List<MTProductDataUploadLogDTO> temp = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data)) {
            temp = data.stream()
                    .filter(dto -> dto.getItemCode().equals(oid))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(temp)) {
                setDataProduceCount(temp, oid, historyDTO);
            } else if (CollectionUtils.isNotEmpty(historyDTO) && historyDTO.stream().anyMatch(n -> n.getItemCode().equals(oid))) {
                List<MTProductDataUploadLogDTO> his = historyDTO.stream().filter(m -> m.getItemCode().equals(oid)).collect(Collectors.toList());
                List<MTProductDataUploadLogDTO> notExistDTO = his.stream().filter(m -> m.getProduceCount().equals(Constant.INT_1)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notExistDTO)) {
                    notExistDTO.forEach(item -> {
                        //设置成0在传美团
                        item.setProduceCount(Constant.ZERO_NUMBER_NAME);
                    });
                    temp.addAll(notExistDTO);
                }
            }
        } else if (CollectionUtils.isNotEmpty(historyDTO)) {
            temp = historyDTO.stream()
                    .filter(dto -> dto.getItemCode().equals(oid))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(temp)) {
                //查找temp集合中itemCode不存在history集合中的数据
                temp.forEach(item -> {
                    //设置成0在传美团
                    item.setProduceCount(Constant.ZERO_NUMBER_NAME);
                });
            }
        }
        return temp;
    }

    /**
     * 调用美团接口
     */
    public void pushToImesB2B(List<MTProductDataUploadLogDTO> data, String empNo, List<String> oidList, List<MtProductDataStatusDTO> dataStatus) throws Exception {
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        MTProductDataUploadDTO model = new MTProductDataUploadDTO();
        OIDDto oidDto = new OIDDto();
        oidDto.setOidList(oidList);
        //获取历史上传数据
        List<MTProductDataUploadLogDTO> historyDTO = meiTuanRepository.getHistoryProductDataUpload(oidDto);
        List<MTProductDataUploadLogDTO> editlist = new ArrayList<>();
        List<MTProductDataUploadLogDTO> temp = new ArrayList<>();
        for (String oid : oidList) {
            //按OID组装推送iMES的数据
            temp = setData(data, oid, historyDTO);
            if (CollectionUtils.isEmpty(temp)) {
                continue;
            }
            //单号总含单据总数：-1不含总数
            int total = temp.get(NumConstant.NUM_ZERO).getItemTotal();

            List<MTProductDataUploadLogDTO> result;
            //有需要失效的先处理失效，不更新job时间，下次job再生效
            if (temp.stream().anyMatch(m -> m.getProduceCount().equals(Constant.INT_0))) {
                result = temp.stream().filter(m -> m.getProduceCount().equals(Constant.INT_0)).collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                MTProductDataUploadLogDTO::getProduceWoCode,
                                m -> m,
                                (m1, m2) -> m1
                        ),
                        map -> new ArrayList<>(map.values())
                ));
            } else {
                result = temp.stream().filter(m -> !m.getProduceCount().equals(Constant.INT_0)).collect(Collectors.toList());
            }
            String statusStr = updateStatus(oid, result, dataStatus, total);
            if (statusStr.equals(Constant.PROCESS_SUCESS_STRING)) {
                continue;
            }
            editlist.addAll(result);

            //如果单据号含有数量，且待推送数据数量小于总数，则追加待定选项
            if (total > NumConstant.NUM_ZERO && result.size() < total) {
                MTProductDataUploadLogDTO pendingDto = new MTProductDataUploadLogDTO();
                pendingDto.setItemCode(oid);
                pendingDto.setProduceCount(total - result.size());
                pendingDto.setProduceWoCode(Constant.BE_PENDING);
                pendingDto.setAssemblyComplateTime("");
                pendingDto.setEstimatedWarehousingTime("");
                pendingDto.setPreparingTime("");
                pendingDto.setPullMaterialComplateTime("");
                pendingDto.setTestComplateTime("");
                pendingDto.setWarehousingComplateTime("");
                result.add(pendingDto);
            }

            // 组装对象
            CustomerDataLogDTO dto = new CustomerDataLogDTO();
            dto.setId(java.util.UUID.randomUUID().toString().trim().replaceAll("-", ""));
            dto.setOrigin(Constant.MES);
            dto.setCustomerName(Constant.MEITUAN);
            dto.setContractNo("");
            dto.setTaskNo(oid);
            dto.setSn("");
            dto.setProjectName("");
            dto.setMessageType(Constant.ZTEIMES_MEITUAN_SERVER);
            dto.setFactoryId(Constant.INT_51);
            model.setBidProduces(result);
            if (statusStr.equals(Constant.CONST_STOCKRETURN)) {
                dto.setJsonData(JSON.toJSONString(model));
            } else {
                dto.setJsonData(JSON.toJSONString(model, SerializerFeature.WriteMapNullValue));
            }
            dto.setCreateBy(empNo);
            dto.setLastUpdatedBy(empNo);
            dataList.add(dto);
        }
        this.pushData(historyDTO, editlist, dataList, dataStatus);
    }

    public String updateStatus(String oid, List<MTProductDataUploadLogDTO> result, List<MtProductDataStatusDTO> dataStatus, int total) throws Exception {
        String statusStr = StringUtils.EMPTY;
        MtProductDataStatusDTO data = null;
        if (dataStatus.stream().anyMatch(m -> m.getMemo().equals(oid))) {
            data = dataStatus.stream().filter(m -> m.getMemo().equals(oid)).collect(Collectors.toList()).get(Constant.INT_0);
        }
        int totalCount = total > NumConstant.NUM_ZERO ? total : result.size();
        if (data == null) {
            data = new MtProductDataStatusDTO();
            data.setMemo(oid);
            data.setTotalCount(totalCount);
            data.setIsAdd(Constant.INT_1);
            data.setComplateCount(result.stream().filter(m -> !StringUtils.isEmpty(m.getWarehousingComplateTime())).collect(Collectors.toList()).size());
            data.setPreStatus(data.getComplateCount().equals(data.getTotalCount()) ? Constant.PROCESS_SUCESS_STRING : Constant.PROCESS_STATUS_STRING);
            dataStatus.add(data);
        } else {
            if (result.stream().anyMatch(m -> m.getProduceCount().equals(Constant.INT_0))) {//失效先把状态修改成待推送
                data.setPreStatus(Constant.PROCESS_STATUS_STRING);
            } else {
                List<MTEntitiesStatusDTO> esData = meiTuanRepository.getEntitiesStatus(oid);
                statusStr = isStockReturn(result, esData, data);
                if (Boolean.TRUE.equals(updateConditionOne(data))) {
                    statusStr = Constant.PROCESS_SUCESS_STRING;
                    dataStatus.remove(data);
                } else if (Boolean.TRUE.equals(updateConditionTwo(data, result))) {
                    data.setPreStatus(Constant.PROCESS_SUCESS_STRING);
                    data.setComplateCount(totalCount);
                    data.setTotalCount(totalCount);
                } else {
                    data.setPreStatus(Constant.PROCESS_STATUS_STRING);
                    data.setComplateCount(result.stream().filter(m -> !StringUtils.isEmpty(m.getWarehousingComplateTime())).collect(Collectors.toList()).size());
                    data.setTotalCount(totalCount);
                }
            }
        }
        return statusStr;
    }

    private Boolean updateConditionOne(MtProductDataStatusDTO data) {
        return (StringUtils.isNotEmpty(data.getStatus()) && data.getStatus().equals(Constant.PROCESS_SUCESS_STRING));
    }

    private Boolean updateConditionTwo(MtProductDataStatusDTO data, List<MTProductDataUploadLogDTO> result) {
        return (StringUtils.isEmpty(data.getStatus()) || !data.getStatus().equals(Constant.CONST_STOCKRETURN))
                && result.stream().allMatch(m -> !StringUtils.isEmpty(m.getWarehousingComplateTime()));
    }

    public String isStockReturn(List<MTProductDataUploadLogDTO> result, List<MTEntitiesStatusDTO> esData, MtProductDataStatusDTO data) throws Exception {
        String statusStr = StringUtils.EMPTY;
        for (MTProductDataUploadLogDTO item : result) {
            if (esData.stream().anyMatch(m -> m.getProduceWoCode().equals(item.getProduceWoCode()))) {
                MTEntitiesStatusDTO esItem = esData.stream().filter(m -> m.getProduceWoCode().equals(item.getProduceWoCode())).collect(Collectors.toList()).get(0);
                if (esItem.getTkDate() != null && esItem.getTkDate().compareTo(esItem.getRkDate()) > Constant.INT_0) {//退库
                    data.setPreStatus(Constant.CONST_STOCKRETURN);
                    statusStr = Constant.CONST_STOCKRETURN;
                    item.setWarehousingComplateTime(null);
                }
            }
        }
        return statusStr;
    }

    public void pushData(List<MTProductDataUploadLogDTO> historyDTO, List<MTProductDataUploadLogDTO> editlist,
                         List<CustomerDataLogDTO> dataList, List<MtProductDataStatusDTO> dataStatus) throws Exception {
        List<MTProductDataUploadLogDTO> result = new ArrayList<>();
        if (!dataList.isEmpty()) {
            result = pushB2B(editlist, dataList);
        }
        if (CollectionUtils.isNotEmpty(result)) {
            if (CollectionUtils.isNotEmpty(historyDTO)) {
                List<MTProductDataUploadLogDTO> update = result.stream().filter(m -> historyDTO.stream().anyMatch(n -> n.getItemCode().equals(m.getItemCode()) && n.getProduceWoCode().equals(m.getProduceWoCode()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(update)) {
                    update(update);
                }
                List<MTProductDataUploadLogDTO> add = result.stream().filter(m -> historyDTO.stream().noneMatch(n -> n.getItemCode().equals(m.getItemCode()) && n.getProduceWoCode().equals(m.getProduceWoCode()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(add)) {
                    insert(add);
                }
            } else {
                insert(result);
            }
            updateDataStatus(dataStatus);
        }
    }

    private List<MTProductDataUploadLogDTO> pushB2B(List<MTProductDataUploadLogDTO> editlist,
                                                    List<CustomerDataLogDTO> dataList) {
        List<MTProductDataUploadLogDTO> result = new ArrayList<>();
        // 推送B2B
        for (List<CustomerDataLogDTO> tempList : CommonUtils.splitList(dataList, Constant.INT_1)) {
            try {
                centerfactoryRemoteService.pushDataToB2B(tempList);
                result.addAll(editlist.stream().filter(m -> m.getItemCode().equals(tempList.get(Constant.INT_0).getTaskNo())).collect(Collectors.toList()));
            } catch (Exception e) {
                //超时情况无需处理 下次重试
            }
        }
        return result;
    }

    public void updateDataStatus(List<MtProductDataStatusDTO> dataStatus) {
        List<MtProductDataStatusDTO> upData = dataStatus.stream().filter(m -> m.getIsAdd().equals(Constant.INT_0)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(upData)) {
            updData(upData);
        }
        List<MtProductDataStatusDTO> addData = dataStatus.stream().filter(m -> m.getIsAdd().equals(Constant.INT_1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addData)) {
            insData(addData);
        }
    }

    public void updData(List<MtProductDataStatusDTO> update) {
        for (List<MtProductDataStatusDTO> tempList : CommonUtils.splitList(update, Constant.INT_100)) {
            meiTuanRepository.batchUpdateProductDataStatusDTO(tempList);
        }
    }

    public void insData(List<MtProductDataStatusDTO> add) {
        for (List<MtProductDataStatusDTO> tempList : CommonUtils.splitList(add, Constant.INT_100)) {
            meiTuanRepository.insertMtProductDataStatusDTO(tempList);
        }
    }

    public void update(List<MTProductDataUploadLogDTO> update) {
        for (List<MTProductDataUploadLogDTO> tempList : CommonUtils.splitList(update, Constant.INT_100)) {
            meiTuanRepository.batchUpdateHistoryProductDataUpload(tempList);
        }
    }

    public void insert(List<MTProductDataUploadLogDTO> add) {
        for (List<MTProductDataUploadLogDTO> tempList : CommonUtils.splitList(add, Constant.INT_100)) {
            meiTuanRepository.insertHistoryProductDataUpload(tempList);
        }
    }
    /* Ended by AICoder, pid:d4df2gcba6h00d514b220942922a586d7884dd78 */
}
