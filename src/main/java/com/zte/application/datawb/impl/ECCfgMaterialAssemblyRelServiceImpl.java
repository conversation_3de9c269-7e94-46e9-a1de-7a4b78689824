package com.zte.application.datawb.impl;

import com.zte.application.datawb.ECCfgMaterialAssemblyRelService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.ECMaterialAssemblyDTO;
import com.zte.interfaces.dto.ECWsmAssembleLinesEntityWithNameDTO;
import com.zte.interfaces.dto.WsmAssembleLinesEntityDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.springbootframe.common.Constants.STR_ONE;

/**
 * 配置物料装配关系查询实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 10:20:00
 */
public class ECCfgMaterialAssemblyRelServiceImpl implements ECCfgMaterialAssemblyRelService {

    @Autowired
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    /**
     * 根据服务器SN列表查询物料装配关系
     *
     * @param serverSnList 服务器SN列表
     * @return 物料装配关系列表
     */
    @Override
    public List<ECMaterialAssemblyDTO> getAssemblyRelationList(List<String> serverSnList) {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();
        for (String serverSn : serverSnList) {
            List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSn);
            if (CollectionUtils.isEmpty(cpmConfigItemAssembleDTOList)) {
                continue;
            }
            // 获取任务对应的装配物料
            List<CpmConfigItemAssembleDTO> configDetailDTOList = wsmAssembleLinesService.getAssemblyMaterialsByEntityId(
                    Integer.valueOf(cpmConfigItemAssembleDTOList.get(0).getEntityId()));
            if (CollectionUtils.isEmpty(configDetailDTOList)) {
                continue;
            }
            // 物料代码去重
            List<String> itemCodeList = configDetailDTOList.stream().map(CpmConfigItemAssembleDTO::getItemCode)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            // 获取服务器sn对应的装配物料
            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = wsmAssembleLinesService.getAssemblyMaterialsWithEntityName(serverSn);
            if (CollectionUtils.isEmpty(wsmAssembleLinesList)) {
                return null;
            }
            // 部件信息查询需要查询三层，物料代码为1开头的需要再向下查询
            // TODO 修改为查询四层（新建四层方法）

            // 机框模组物料代码字典查询
            List<SysLookupValues> lookupValues = wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE);
            List<String> shelfModItemCodeList = lookupValues.stream().map(SysLookupValues::getDescription)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            List<String> shelfModItemBarcodeList = configDetailDTOList.stream().filter(i ->
                    shelfModItemCodeList.contains(i.getItemCode())).map(CpmConfigItemAssembleDTO::getItemBarcode).collect(Collectors.toList());
            // 机框模组查询三层装配关系
            // TODO 修改为查询四层（新建四层方法）
            getThreeWsmAssembleLines(shelfModItemBarcodeList, wsmAssembleLinesList);
        }
        return list;
    }

    public void getAllWsmAssembleLines(List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {
        List<String> itemBarcodeList2 = wsmAssembleLinesList.stream().filter(
                i->i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(itemBarcodeList2)) {
            // 查询第2层
            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList2 = wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList2);
            if (!CollectionUtils.isEmpty(wsmAssembleLinesList2)) {
                wsmAssembleLinesList.addAll(wsmAssembleLinesList2);
                List<String> itemBarcodeList3 = wsmAssembleLinesList2.stream().filter(
                        i->i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(itemBarcodeList3)) {
                    // 查询第3层
                    List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList3 = wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList3);
                    if (!CollectionUtils.isEmpty(wsmAssembleLinesList3)) {
                        wsmAssembleLinesList.addAll(wsmAssembleLinesList3);
                    }
                }
            }
        }
    }

    /**
     * 机框模组查询三层装配关系
     */
    public void getThreeWsmAssembleLines(List<String> itemBarcodeList, List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {
        // 第一层
        if (CollectionUtils.isEmpty(itemBarcodeList)) {
            return;
        }
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList1 = wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList1)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList1);
        }

        // 第二层
        List<String> itemBarcodeList2 = wsmAssembleLinesList1.stream().filter(
                i -> i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemBarcodeList2)) {
            List<String> newitemBarcodeList = wsmAssembleLinesList1.stream().map(m -> m.getItemBarcode()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newitemBarcodeList)) {
                return;
            }
            newitemBarcodeList.removeAll(itemBarcodeList);
            getThreeWsmAssembleLines(newitemBarcodeList, wsmAssembleLinesList);
            return;
        }
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList2 = wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList2);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList2)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList2);
        }

        // 第三层
        List<String> itemBarcodeList3 = wsmAssembleLinesList2.stream().filter(
                i -> i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemBarcodeList3)) {
            return;
        }
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList3 = wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList3);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList3)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList3);
        }
    }
}
