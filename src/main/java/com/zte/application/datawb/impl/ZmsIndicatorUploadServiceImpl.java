package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsDeviceInventoryUploadService;
import com.zte.application.datawb.ZmsIndicatorUploadService;
import com.zte.application.sfc.WholeMachineUpTestRecordService;
import com.zte.common.CommonUtils;
import com.zte.common.enums.StationLogFileTypeEnum;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.MtlSystemItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static java.util.UUID.randomUUID;


/**
 * <AUTHOR>
 */
@Service
@DataSource(DatabaseType.SFC)
public class ZmsIndicatorUploadServiceImpl implements ZmsIndicatorUploadService {
    private static final Logger logger = LoggerFactory.getLogger(ZmsIndicatorUploadServiceImpl.class);
    @Autowired
    private ZmsIndicatorUploadRepository zmsIndicatorUploadRepository;
    @Autowired
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Autowired
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;
    @Autowired
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Autowired
    private MtlSystemItemsRepository mtlSystemItemsRepository;
    @Autowired
    private ZmsDeviceInventoryUploadService zmsDeviceInventoryUploadService;
    @Autowired
    private ZmsMesB2bUploadLogRepository zmsMesB2bUploadLogRepository;
    @Autowired
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Autowired
    private WholeMachineUpTestRecordService wholeMachineUpTestRecordService;
    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Autowired
    private CfgCodeRuleItemServiceImpl  cfgCodeRuleItemService;
    @Autowired
    private ZmsAlibabaRepository zmsAlibabaRepository;
    @Autowired
    private MeiTuanRepository meiTuanRepository;


    @Override
    public void indicatorUpload(ZmsInputDTO zmsInputDTO) throws Exception {

        // 查询还未上传液冷数据的生产工站信息
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        List<ZmsStationInfoDTO> zmsStationInfoDTOList = zmsIndicatorUploadRepository.getIndicatorZmsStationInfoList(zmsInputDTO);
        if (CommonUtils.isEmpty(zmsStationInfoDTOList)) {
            return;
        }

        // 根据SN调用中试接口获取液冷数据
        List<String> snList = zmsStationInfoDTOList.stream().map(ZmsStationInfoDTO::getServerSn).distinct().collect(Collectors.toList());
        String strToken = zmsStationLogUploadService.getToken();
        String zsUrl = zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200012);
        List<List<String>> snLists = CommonUtils.splitList(snList, NumConstant.NUM_FIVE);
        List<ZmsIndicatorDTO> zmsIndicatorDTOAllList = new ArrayList<>();
        for (List<String> list : snLists) {
            // 调用中试接口
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(PART_CODE_LIST, list);
            String bo = zmsStationLogUploadService.getZsStationLog(paramMap, zsUrl, strToken);
            List<ZmsIndicatorDTO> zmsIndicatorDTOList = JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<ZmsIndicatorDTO>>() {
                    });
            if (CommonUtils.isNotEmpty(zmsIndicatorDTOList)) {
                zmsIndicatorDTOAllList.addAll(zmsIndicatorDTOList);
            }
        }

        // 如果没有获取到液冷数据，更新生产工站信息的最后更新时间
        if (CommonUtils.isEmpty(zmsIndicatorDTOAllList)) {
            List<String> updateIdList = zmsStationInfoDTOList.stream().map(ZmsStationInfoDTO::getId).distinct().collect(Collectors.toList());
            for (List<String> tempList : CommonUtils.splitList(updateIdList, NumConstant.NUM_FIVE_HUNDRED)) {
                zmsIndicatorUploadRepository.updateZmsStationInfo(tempList);
            }
            return;
        }

        // 组装数据
        getIndicatorData(zmsInputDTO, zmsStationInfoDTOList, zmsIndicatorDTOAllList);
    }

    /**
     * 组装数据
     */
    public void getIndicatorData(ZmsInputDTO zmsInputDTO, List<ZmsStationInfoDTO> zmsStationInfoDTOList,
                                 List<ZmsIndicatorDTO> zmsIndicatorDTOAllList) throws Exception {

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        List<String> updateIdList = new ArrayList<>();

        for (ZmsStationInfoDTO zmsStationInfoDTO : zmsStationInfoDTOList) {
            // 根据SN+stationId，过滤对应的液冷数据
            ZmsIndicatorDTO zmsIndicatorDTO = zmsIndicatorDTOAllList.stream().filter(i ->
                    zmsStationInfoDTO.getServerSn().equals(i.getServerSn()) && zmsStationInfoDTO.getStationId().equals(i.getStationId())).findFirst().orElse(null);
            // 如果没有获取到液冷数据，则更新生产工站信息的最后更新时间
            if (CommonUtils.isEmpty(zmsIndicatorDTO)) {
                updateIdList.add(zmsStationInfoDTO.getId());
                continue;
            }
            // 组装B2B的对象
            CustomerDataLogDTO dto = new CustomerDataLogDTO();
            dto.setId(randomUUID().toString());
            dto.setOrigin(MES);
            dto.setCustomerName(BYTE_DANCE);
            dto.setProjectName(INDICATOR_ZH);
            dto.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
            dto.setContractNo(zmsStationInfoDTO.getStationId());
            dto.setTaskNo(zmsStationInfoDTO.getEntityName());
            dto.setSn(zmsStationInfoDTO.getServerSn());
            dto.setJsonData(JSON.toJSONString(zmsIndicatorDTO));
            dto.setFactoryId(INT_51);
            dto.setCreateBy(zmsInputDTO.getEmpNo());
            dto.setLastUpdatedBy(zmsInputDTO.getEmpNo());
            dataList.add(dto);
            // 组装日志对象
            ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
            BeanUtils.copyProperties(dto, zmsMesInfoUploadDTO);
            zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        }
        // 更新没有液冷数据的生产工站信息的最后更新时间
        updateZmsStationInfo(updateIdList);

        // 写入上传日志
        insertMesInfoUploadLog(zmsMesInfoUploadDTOList);

        // 调用iMES接口上传B2B
        pushDataToB2B(dataList);
    }

    /**
     * 更新没有液冷数据的生产工站信息的最后更新时间
     */
    public void updateZmsStationInfo(List<String> updateIdList) {

        if (CommonUtils.isEmpty(updateIdList)) {
            return;
        }
        for (List<String> tempList : CommonUtils.splitList(updateIdList, NumConstant.NUM_FIVE_HUNDRED)) {
            zmsIndicatorUploadRepository.updateZmsStationInfo(tempList);
        }
    }

    /**
     * 写入上传日志
     */
    public void insertMesInfoUploadLog(List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList) {

        if (CommonUtils.isEmpty(zmsMesInfoUploadDTOList)) {
            return;
        }
        for (List<ZmsMesInfoUploadLogDTO> tempList : CommonUtils.splitList(zmsMesInfoUploadDTOList, NumConstant.NUM_FIVE_HUNDRED)) {
            zmsIndicatorUploadRepository.insertMesInfoUploadLog(tempList);
        }
    }

    /**
     * 调用iMES接口上传B2B
     */
    public void pushDataToB2B(List<CustomerDataLogDTO> dataList) throws Exception {

        if (CommonUtils.isEmpty(dataList)) {
            return;
        }
        for (List<CustomerDataLogDTO> tempList : CommonUtils.splitList(dataList, NumConstant.NUM_FIVE_HUNDRED)) {
            centerfactoryRemoteService.pushDataToB2B(tempList);
        }
    }

    /* Started by AICoder, pid:51f97c5353174018b1d960c7bfee2e82 */
    @Override
    public void updateMesInfoUploadLog(CustomerDataLogDTO dto) throws JsonProcessingException {
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setMessageType(dto.getMessageType());
        zmsMesInfoUploadLogDTO.setId(dto.getId());
        zmsIndicatorUploadRepository.updateMesInfoUploadLog(zmsMesInfoUploadLogDTO);
        switch (dto.getMessageType()){
            // 类型为MDSP整机库存移动数据上传时，需要更新任务上传表的上传标识
            case Constant.ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT:
                String projectName = dto.getProjectName();
                if (StringUtils.isBlank(projectName)) {
                    projectName = "default";
                }
                switch (projectName) {
                    case Constant.DEVICE_WHOLE_INVENTORY_MOVE_ZH:
                        // 整机车间库库存移动
                        zmsMesB2bUploadLogRepository.updateB2bLogByPushId(dto.getId());
                        break;
                    default:
                        // 其他场景
                        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO1 = zmsIndicatorUploadRepository.
                                getZmsMesInfoUploadLogDTOById(dto.getId());
                        zmsDeviceInventoryUploadService.updateCpmContractEntitiesUpload(zmsMesInfoUploadLogDTO1);
                }
                break;

            //MDSP销售订单数据上传
            case Constant.ZTE_IMES_BYTEDANCE_SALES_ORDER:
                ZmsMesInfoUploadLogDTO uploadLogDTO = zmsIndicatorUploadRepository.getZmsMesInfoUploadLogDTOById(dto.getId());
                zmsByteDanceCCEService.updateSalesOrderUpload(uploadLogDTO);
                break;

            //腾讯上传MPT测试数据
            case Constant.MESSAGE_TYPE_B2B_TENCENTMPT:
                wholeMachineUpTestRecordService.updateMptUploadStatus(dto);
                break;

            // 类型为腾讯正向数据时，需要更新腾讯正向数据表的上传标识
            case Constant.ZTE_IMES_TENCENT_FORWARD_UPLOAD:
                zmsForwardTencentRepository.updateForwardTencentByMessageId(dto.getId());
                updateInternetMain(dto.getSn(), LOOKUP_TYPE_824009200001, STR_NUMBER_ONE, STRING_EMPTY);
                break;

            // 类型为阿里测试数据时，需要更新阿里测试数据表的上传标识
            case Constant.ZTE_IMES_ALIBABA_AIS_META_MCT:
                zmsAlibabaRepository.updateAlibabaTestResultByMessageId(dto.getId());
                updateInternetMain(dto.getSn(), LOOKUP_TYPE_824009200005, STR_NUMBER_ONE, STRING_EMPTY);
                break;
            // 阿里测试日志上传，更新上传日志表的推送状态
            case MESSAGE_TYPE_TEST_FILE_ALIBABA:
                StationLogDTO stationLogDTO = new StationLogDTO();
                stationLogDTO.setServerSn(dto.getSn());
                stationLogDTO.setLogName(dto.getContractNo());
                stationLogDTO.setStationId(dto.getTaskNo());
                stationLogDTO.setFileType(StationLogFileTypeEnum.Alibaba.getCode());
                zmsAlibabaRepository.updateAlibabaFileLog(stationLogDTO);
                updateInternetMain(dto.getSn(), LOOKUP_TYPE_824009200014, STR_NUMBER_ONE, STRING_EMPTY);
                break;

            case Constant.ZTEIMES_MEITUAN_SERVER:
                List<MtProductDataStatusDTO> upData = new ArrayList<>();
                MtProductDataStatusDTO statusDTO = new MtProductDataStatusDTO();
                statusDTO.setMemo(dto.getTaskNo());
                statusDTO.setPreToS(1);
                upData.add(statusDTO);
                meiTuanRepository.batchUpdateProductDataStatusDTO(upData);
                break;

            default:break;
        }
        this.testLogCallback(dto);
    }
    /* Ended by AICoder, pid:51f97c5353174018b1d960c7bfee2e82 */

    /**
     * 互联网看板统一更新方法
     *
     * @param serverSn     服务器SN
     * @param dataTypeDic  8240092 字典ID
     * @param dataUpStatus 数据上传状态（默认为0，上传成功=1，上传失败=-1，比对成功=2，比对失败=-2）
     * @param failReason   失败原因，没有传空
     */
    @Override
    public void updateInternetMain(String serverSn, String dataTypeDic,
                                   String dataUpStatus, String failReason) {
        String dataType = cfgCodeRuleItemService.getSmallBoxSize(dataTypeDic);
        List<ZmsInternetMainDTO> zmsInternetMainDTOList = new ArrayList<>();
        ZmsInternetMainDTO zmsInternetMainDTO = new ZmsInternetMainDTO();
        zmsInternetMainDTO.setServerSn(serverSn);
        zmsInternetMainDTO.setDataType(dataType);
        zmsInternetMainDTO.setDataUpStatus(dataUpStatus);
        if (StringUtils.isNotEmpty(failReason)) {
            if (failReason.length() > NumConstant.NUM_EIGHT_HUNDRED) {
                failReason = failReason.substring(NumConstant.NUM_ZERO, NumConstant.NUM_EIGHT_HUNDRED);
            }
            zmsInternetMainDTO.setFailReason(failReason);
        }
        zmsInternetMainDTOList.add(zmsInternetMainDTO);
        zmsForwardTencentRepository.updateInternetMain(zmsInternetMainDTOList);
    }

    private Map<String, BigDecimal> getMessageTypeMap () {
        List<SysLookupValues> messageTypeList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240063);
        if (CollectionUtils.isEmpty(messageTypeList)) {
            return null;
        }
        return messageTypeList.stream().filter(e -> StringUtils.isNotEmpty(e.getDescription()) && e.getLookupCode() != null)
                .collect(Collectors.toMap(SysLookupValues::getDescription, SysLookupValues::getLookupCode));
    }

    /* Started by AICoder, pid:nc521i42d4fc76f14edf08e5702697305ff9de02 */
    public void testLogCallback(CustomerDataLogDTO dto) throws JsonProcessingException {
        // 获取消息类型map
        Map<String, BigDecimal> messageTypeMap = this.getMessageTypeMap();
        if (MapUtils.isEmpty(messageTypeMap)) {
            return;
        }
        // 判断消息类型是否在配置中存在
        BigDecimal messageTypeCode = messageTypeMap.get(dto.getMessageType());
        if (messageTypeCode == null) {
            return;
        }
        // 获取整机测试日志、测试数据及mpt测试日志消息类型对应数据类型map
        Map<BigDecimal, BigDecimal> messageTypeCodeMap = this.getMessageTypeCodeMap();
        BigDecimal dataTypeCode = messageTypeCodeMap.get(messageTypeCode);
        // 不在处理范围返回
        if (dataTypeCode == null) {
            return;
        }
        // 获取数据类型列表
        List<SysLookupValues> dataTypeList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240092);
        if (CollectionUtils.isEmpty(dataTypeList)) {
            return;
        }
        // 使用Map来快速查找数据类型
        Map<BigDecimal, String> dataTypeMap = dataTypeList.stream()
            .filter(lk -> lk.getLookupCode() != null && StringUtils.isNotEmpty(lk.getLookupMeaning()))
            .collect(Collectors.toMap(SysLookupValues::getLookupCode, SysLookupValues::getLookupMeaning));
        dto.setDataType(dataTypeMap.get(dataTypeCode));
        // mpt测试日志上传
        if (new BigDecimal(Constant.LOOKUP_TYPE_824006300015).compareTo(messageTypeCode) == 0) {
            wholeMachineUpTestRecordService.updateMptUploadStatus(dto);
            return;
        }
        // 更新整机测试数据上传状态
        wholeMachineUpTestRecordService.updateUploadStatus(dto);
    }

    private Map<BigDecimal, BigDecimal> getMessageTypeCodeMap () {
        Map<BigDecimal, BigDecimal> map = new HashMap<>();
        map.put(new BigDecimal(Constant.LOOKUP_TYPE_824006300013), new BigDecimal(Constant.LOOKUP_TYPE_824009200004));
        map.put(new BigDecimal(Constant.LOOKUP_TYPE_824006300014), new BigDecimal(Constant.LOOKUP_CODE_MACHINE_TEST_LOG_DATA_TYPE));
        map.put(new BigDecimal(Constant.LOOKUP_TYPE_824006300015), new BigDecimal(Constant.LOOKUP_TYPE_824009200002));
        return map;
    }

    /* Ended by AICoder, pid:nc521i42d4fc76f14edf08e5702697305ff9de02 */

    /**
     * Started by AICoder, pid:c7db0e7b79da48a788140ea82e698cd5
     * MDSP生产订单收货接口
     */
    @Override
    public void prodOrderDelivery(ProdOrderDeliveryReqDTO dto, String empNo) throws Exception {

        List<EntityWeightDTO> dictCompanys = mesGetDictInforRepository.getDict(Constant.LOOKUP_TYPE_3020035);

        if (CollectionUtils.isNotEmpty(dictCompanys)) {
            dto.setCompanys(dictCompanys.stream().map(f -> f.getDescription()).collect(Collectors.toList()));
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        List<ProdOrderDeliveryDTO> listProdOrderDelivery = mtlSystemItemsRepository.getProdOrderDelivery(dto);
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);

        if (CollectionUtils.isEmpty(listProdOrderDelivery)) {
            return;
        }

        String pageSize = mesGetDictInforRepository.getDicDescription(Constant.LOOKUP_TYPE_302003600001);
        for (List<ProdOrderDeliveryDTO> tempList : CommonUtils.splitList(listProdOrderDelivery, Integer.valueOf(pageSize))) {
            handleProdOrderDelivery(tempList, empNo, zmsDeviceInventoryUploadService.getDataTransferBatchNo());
        }
    }/* Ended by AICoder, pid:c7db0e7b79da48a788140ea82e698cd5 */

    /**
     * Started by AICoder, pid:db7bf4e60cde49d198ea5752dddc8163
     * MDSP生产订单收货接口 组装数据
     *
     * @param listProdOrderDelivery
     * @param empNo
     */
    public void handleProdOrderDelivery(List<ProdOrderDeliveryDTO> listProdOrderDelivery, String empNo, String batchNo) throws Exception {
        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsDeviceInventoryUploadService.getLookupValuesList(zmsInputDTO);
        List<ZmsCbomInfoDTO> listZmsCbomInfo = zmsDeviceInventoryUploadService.getMaterialInfo
                (listProdOrderDelivery.stream().map(f -> f.getEntityName()).distinct().collect(Collectors.toList()), zmsInputDTO);
        for (ProdOrderDeliveryDTO item : listProdOrderDelivery) {
            List<ProdOrderDeliveryItemDTO> listProdOrderDeliveryItem = new ArrayList<>();
            item.setDataTransferBatchNo(batchNo);
            List<String> listEntryLineId = Arrays.asList(item.entryLineIds.split(Constant.COMMA));
            for (String list : listEntryLineId) {
                ProdOrderDeliveryItemDTO dto = new ProdOrderDeliveryItemDTO();

                dto.setMaterialDocNo(item.getMaterialDocNo());
                dto.setMaterialDocLine(list);
                dto.setOdmPlantCode(Constant.MES_BATCHNO);
                dto.setQuantity(Constant.INT_1);
                dto.setUnit(Constant.STRING_GE);
                dto.setOdmStorageLoc(Constant.STRING_N003);
                dto.setMovementType(Constant.STRING_RECEIPT);
                dto.setReverseIndicator(STRING_EMPTY);

                List<ZmsCbomInfoDTO> query = listZmsCbomInfo.stream().filter(f -> item.getEntityName().equals(f.getEntityName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(query)) {
                    listProdOrderDeliveryItem.add(dto);
                    continue;
                }
                List<ZmsExtendedAttributeDTO> queryExtendedAttributes = query.get(INT_0).getExtendedAttributes();
                if (CollectionUtils.isEmpty(queryExtendedAttributes)) {
                    listProdOrderDeliveryItem.add(dto);
                    continue;
                }
                List<ZmsExtendedAttributeDTO> queryCode = queryExtendedAttributes.stream().filter(f -> Constant.CBOM_EXTENTION_ZH_CODE.equals(f.getCbomExtentionZh())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(queryCode)) {
                    dto.setMaterialCode(queryCode.get(INT_0).getCbomExtentionValue());
                }
                List<ZmsExtendedAttributeDTO> queryDesc = queryExtendedAttributes.stream().filter(f -> CBOM_EXTENTION_ZH_NAME.equals(f.getCbomExtentionZh())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(queryDesc)) {
                    dto.setMaterialDesc(queryDesc.get(INT_0).getCbomExtentionValue());
                }
                listProdOrderDeliveryItem.add(dto);
            }
            item.setProdOrderDeliveryItems(listProdOrderDeliveryItem);
            item.setEntityName(null);
            item.setEntryLineIds(null);
        }
        this.pushDataToB2BToImes(empNo, listProdOrderDelivery, batchNo);
    }/* Ended by AICoder, pid:db7bf4e60cde49d198ea5752dddc8163 */


    /**
     * Started by AICoder, pid:e04e353cccbe4f38b82bcba0f7a868f6
     * 推送B2B
     */
    public void pushDataToB2BToImes(String empNo, List<ProdOrderDeliveryDTO> tempInsertList, String batchNo) throws Exception {
        if (CollectionUtils.isEmpty(tempInsertList)) {
            return;
        }

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(randomUUID().toString().replace("-", ""));
        customerDataLogDTO.setTid(null);
        customerDataLogDTO.setOrigin(Constant.MES);
        customerDataLogDTO.setCustomerName(Constant.BYTE_DANCE);
        customerDataLogDTO.setProjectName(Constant.ZTE_PROD_ORDER);
        customerDataLogDTO.setProjectPhase(tempInsertList.get(INT_0).getDataDate());
        customerDataLogDTO.setCooperationMode(null);
        customerDataLogDTO.setMessageType(Constant.ZTEiMES_BYTEDANCE_GOODRECEIPSOFMO);
        customerDataLogDTO.setCraftSection(null);
        customerDataLogDTO.setTaskNo(batchNo);
        customerDataLogDTO.setItemNo(null);
        customerDataLogDTO.setSn(null);

        Map<String, Object> map = new HashMap<>();
        map.put(DATA, tempInsertList);

        customerDataLogDTO.setJsonData(JSON.toJSONString(map));
        customerDataLogDTO.setErrMsg(null);
        customerDataLogDTO.setBeginTime(null);
        customerDataLogDTO.setEndTime(null);
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setStatus(null);
        customerDataLogDTO.setRemark(null);
        customerDataLogDTO.setCreateBy(empNo);
        customerDataLogDTO.setCreateDate(null);
        customerDataLogDTO.setLastUpdatedBy(empNo);
        customerDataLogDTO.setLastUpdatedDate(null);
        customerDataLogDTO.setEnabledFlag(null);
        dataList.add(customerDataLogDTO);

        // 组装日志对象
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
        BeanUtils.copyProperties(customerDataLogDTO, zmsMesInfoUploadDTO);
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        // 写入上传日志
        insertMesInfoUploadLog(zmsMesInfoUploadDTOList);

        centerfactoryRemoteService.pushDataToB2B(dataList);

    }/* Ended by AICoder, pid:e04e353cccbe4f38b82bcba0f7a868f6 */
}
