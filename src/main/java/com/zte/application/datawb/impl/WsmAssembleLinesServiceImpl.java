/**
 * \u9879\u76ee\u540d\u79f0 : zte-mes-manufactureshare-datawbsys
 * \u521b\u5efa\u65e5\u671f : 2018-12-17
 * \u4fee\u6539\u5386\u53f2 :
 *   1. [2018-12-17] \u521b\u5efa\u6587\u4ef6 by 10095344
 **/
package com.zte.application.datawb.impl;

import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.utils.BusinessConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLines;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.interfaces.dto.WsmAssembleLinesEntityDTO;
import com.zte.interfaces.dto.ECWsmAssembleLinesEntityWithNameDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.constants.NumConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * \u63a5\u53e3\u529f\u80fd\u63cf\u8ff0
 * 
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.SFC)
public class WsmAssembleLinesServiceImpl implements WsmAssembleLinesService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    /**
     * \u63a5\u53e3\u529f\u80fd\u63cf\u8ff0
     * 
     * @param line
     * @return WsmAssembleLines
     **/

	@Override
	public List<WsmAssembleLines> selectMainBarcodeBySubBarcode(WsmAssembleLines line) {

		return wsmAssembleLinesRepository.selectMainBarcodeBySubBarcode(line);
	}

	@Override
	public List<WsmAssembleLinesEntityDTO> getAssemblyMaterials(String itemBarcode) {
		if(StringUtils.isEmpty(itemBarcode)){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getAssemblyMaterials(itemBarcode);
	}

	@Override
	public List<ECWsmAssembleLinesEntityWithNameDTO> getAssemblyMaterialsWithEntityName(String itemBarcode) {
		if(StringUtils.isEmpty(itemBarcode)){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getAssemblyMaterialsWithEntityName(itemBarcode);
	}

	@Override
	public List<WsmAssembleLinesEntityDTO> getAssemblyMaterialList(List<String> itemBarcodeList) {
		if(CollectionUtils.isEmpty(itemBarcodeList)){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getAssemblyMaterialList(itemBarcodeList);
	}

	@Override
	public List<ECWsmAssembleLinesEntityWithNameDTO> getAssemblyMaterialListWithEntityName(List<String> itemBarcodeList) {
		if(CollectionUtils.isEmpty(itemBarcodeList)){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getAssemblyMaterialListWithEntityName(itemBarcodeList);
	}

	/**
	 * 根据任务id获取装配物料-整机数据回传专用
	 * @param entityId
	 * @return
	 */
	@Override
	public List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByEntityId(Integer entityId) {
		if(StringUtils.isEmpty(entityId+"")){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getAssemblyMaterialsByEntityId(entityId);
	}



	/**
	 * 根据erp入库时间-整机数据回传专用
	 * @param entityId
	 * @return
	 */
	@Override
	public String getCustomerPONumber(Integer entityId) {
		return wsmAssembleLinesRepository.getCustomerPONumber(entityId);
	}

	/**
	 * 数据字典
	 * @param lookupType
	 * @return
	 */
	@Override
	public List<SysLookupValues> getSysLookupValues(String lookupType) {
		if(StringUtils.isEmpty(lookupType)){
			return new ArrayList<>();
		}
		return wsmAssembleLinesRepository.getSysLookupValues(lookupType);
	}

	/**
	 * 查询装配关系
	 * <AUTHOR> 袁海洋
	 */
	@Override
	@DataSource(DatabaseType.SFC)
	public ServiceData getWsmAssembleLinesDTOPage(WsmAssembleLinesDTO dto) {
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));

		Map<String, Object> map = new HashMap<>();
		// 设置查询条件
		map.put(BusinessConstant.MAIN_ITEM_BARCODE, dto.getMainItemBarcode());
		map.put(BusinessConstant.SUB_ITEM_BARCODE, dto.getSubItemBarcode());
		long page = (dto.getPageString() == null ? 0 : Integer.parseInt(dto.getPageString())) < 1 ? 1 : Integer.parseInt(dto.getPageString());
		long rows = (dto.getRowsString() == null ? 0 : Integer.parseInt(dto.getRowsString())) < 1 ? NumConstant.NUM_5000 : Integer.parseInt(dto.getRowsString());
		map.put(BusinessConstant.START_ROW, (page - 1) * rows + 1);
		map.put(BusinessConstant.END_ROW, page * rows);
		List<WsmAssembleLinesDTO> wsmAssembleLinesDTOList = wsmAssembleLinesRepository.getWsmAssembleLinesDTOPage(map);
		logger.info("装配关系查询: {}", wsmAssembleLinesDTOList);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(wsmAssembleLinesDTOList);
		return ret;
	}

	@Override
	@DataSource(DatabaseType.SFC)
	public ServiceData getWsmAssembleLinesDTOCount(WsmAssembleLinesDTO dto) {
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));

		Map<String, Object> map = new HashMap<String, Object>();
		// 设置查询条件
		map.put(BusinessConstant.MAIN_ITEM_BARCODE, dto.getMainItemBarcode());
		map.put(BusinessConstant.SUB_ITEM_BARCODE, dto.getSubItemBarcode());
		long total = wsmAssembleLinesRepository.getWsmAssembleLinesDTOCount(map);
		logger.info("装配关系查询总数: {}", total);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(total);
		return ret;
	}

	/**
	 * 根据主子条码获取已存在的装配关系-推送装配关系到MES专用
	 * @param paramDTO
	 * @return
	 */
	@Override
	@DataSource(DatabaseType.SFC)
	public List<WsmAssembleLinesDTO> getWsmAssemblyListByMainAndSub(WsmAssembleLinesDTO paramDTO) {
		List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS =wsmAssembleLinesRepository.getWsmAssemblyListByMainAndSub(paramDTO);
		return wsmAssembleLinesDTOS;
	}


	@Override
	public int deleteAssembleLineForRemoveRepeat() {
		return wsmAssembleLinesRepository.deleteAssembleLineForRemoveRepeat();
	}
}