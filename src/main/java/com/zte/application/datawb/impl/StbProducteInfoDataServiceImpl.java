package com.zte.application.datawb.impl;

import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.application.datawb.StbProducteInfoDataService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.datawb.StbProdDeliveryInfoQueryExtend;
import com.zte.domain.model.datawb.StbProductSnCount;
import com.zte.domain.model.datawb.StbProducteInfoDataRepository;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * STB_PRODUCTE_INFO 表查询ServiceImpl
 *
 * <AUTHOR>
 */
@Service
@DataSource("SFC")
public class StbProducteInfoDataServiceImpl implements StbProducteInfoDataService {

    @Autowired
    private StbProducteInfoDataRepository stbProducteInfoDataRepository;

    @Autowired
    private CpeBoxupBillService cpeBoxupBillService;

    /**
     * 机顶盒发货信息SFC.STB_PRODUCTE_INFO表字段查询
     */
    @Override
    public List<StbProdDeliveryInfoQueryExtend> getListStbProdDeliveryInfo(String barcodes) {
        return stbProducteInfoDataRepository.getListStbProdDeliveryInfo(barcodes);
    }

    /**
     * 机顶盒发货数量查询
     */
    @Override
    public Map<String, Integer> getDhomeSnListCount(List<String> snStartList, Integer daysAgoStart, Integer daysAgoEnd) {
        if (CollectionUtils.isEmpty(snStartList)) {
            return new HashMap<>();
        }
        if (daysAgoStart == null) {
            daysAgoStart = 1;
        }
        if (daysAgoEnd == null) {
            daysAgoEnd = 0;
        }
        List<String> collect = snStartList
                .stream()
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<StbProductSnCount> snCountList = stbProducteInfoDataRepository.getDhomeSnCount(collect, daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isEmpty(snCountList)) {
            snCountList = new ArrayList<>();
        }
        Map<String, Integer> map = snCountList.stream().collect(Collectors.toMap(StbProductSnCount::getSnStart, StbProductSnCount::getCount));
        List<String> itemBarcodes = cpeBoxupBillService.getItemBarcodesDaysAgo(daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isEmpty(itemBarcodes)) {
            return map;
        }
        List<List<String>> itemBarcodeList = CommonUtils.splitList(itemBarcodes, NumConstant.NUM_ONE_THOUSAND);
        List<StbProductSnCount> snCountByItemBarcodes = new ArrayList<>();
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        for (List<String> itemList : itemBarcodeList) {
            snCountByItemBarcodes.addAll(stbProducteInfoDataRepository.getDhomeSnCountByProductSnList(collect, itemList));
        }
        if (CollectionUtils.isEmpty(snCountByItemBarcodes)) {
            return map;
        }
        for (StbProductSnCount snCountByItemBarcode : snCountByItemBarcodes) {
            map.put(snCountByItemBarcode.getSnStart(),
                    map.getOrDefault(snCountByItemBarcode.getSnStart(), 0) + snCountByItemBarcode.getCount());
        }
        return map;
    }

    @Override
    public List<String> getDhomeSnListBySnStart(String snStart, Integer daysAgoStart, Integer daysAgoEnd) {
        if (StringUtils.isBlank(snStart)) {
            return Collections.emptyList();
        }
        if (daysAgoStart == null) {
            daysAgoStart = 1;
        }
        if (daysAgoEnd == null) {
            daysAgoEnd = 0;
        }
        List<String> snList = stbProducteInfoDataRepository.getDhomeSnListBySnStart(snStart, daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isEmpty(snList)) {
            snList = new ArrayList<>();
        }
        List<String> itemBarcodes = cpeBoxupBillService.getItemBarcodesDaysAgo(daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isNotEmpty(itemBarcodes)) {
            List<List<String>> itemBarcodeList = CommonUtils.splitList(itemBarcodes, NumConstant.NUM_ONE_THOUSAND);
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
            for (List<String> itemList : itemBarcodeList) {
                snList.addAll(stbProducteInfoDataRepository.getDhomeSnListByItemList(snStart, itemList));
            }
        }
        return snList.stream().distinct().collect(Collectors.toList());
    }
}
