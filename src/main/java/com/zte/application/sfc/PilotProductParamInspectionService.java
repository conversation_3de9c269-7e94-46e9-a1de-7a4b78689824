package com.zte.application.sfc;

import com.zte.interfaces.dto.sfc.PilotProductReqDTO;
import com.zte.interfaces.dto.sfc.PilotProductReqVO;

import java.util.List;
import java.util.Map;

/**
 * @Description 中试个参检验接口service
 * <AUTHOR>
 * @Date 9:55 2025/3/6
 * @Version 1.0
 **/
public interface PilotProductParamInspectionService {


    /**
     * 校验
     *
     * @param productReqDTO
     * @return
     */
    List<PilotProductReqVO> dataCheckIsOnly(PilotProductReqDTO productReqDTO);

    Map<String, Integer> getCpeSnListCount(List<String> snStartList, Integer daysAgoStart, Integer daysAgoEnd);

    /**
     * 根据 SN特征值 查询指定日期内 cpe 发货的SN集合
     *
     * @param snStart
     * @param daysAgoStart
     * @param daysAgoEnd
     * @return List<String>
     */
    List<String> getCpeSnListBySnStart(String snStart, Integer daysAgoStart, Integer daysAgoEnd);
}
