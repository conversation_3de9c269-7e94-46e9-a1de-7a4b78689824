package com.zte.application.sfc.impl;

import com.google.common.collect.Lists;
import com.zte.application.MesGetDictInforService;
import com.zte.application.barcode.MesOmsGetDictInforService;
import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.application.sfc.PilotProductParamInspectionService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.CommonUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.JobsSupplierProduceDataRepository;
import com.zte.domain.model.datawb.StbProductSnCount;
import com.zte.domain.model.datawb.StbProducteInfoDataRepository;
import com.zte.domain.model.sfc.JobsSupplierProduceDataTacRepository;
import com.zte.domain.model.sfc.StbProducteInfoTacRepository;
import com.zte.interfaces.dto.api.DictInfoForDTO;
import com.zte.interfaces.dto.sfc.PilotProductDTO;
import com.zte.interfaces.dto.sfc.PilotProductParamVO;
import com.zte.interfaces.dto.sfc.PilotProductReqDTO;
import com.zte.interfaces.dto.sfc.PilotProductReqVO;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 中试个参检验接口service
 * <AUTHOR>
 * @Date 9:56 2025/3/6
 * @Version 1.0
 **/
@Service
@DataSource(value = DatabaseType.SFC)
public class PilotProductParamInspectionServiceImpl implements PilotProductParamInspectionService {
    @Autowired
    private MesGetDictInforService mesGetDictInforService;
    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Autowired
    private MesOmsGetDictInforService mesOmsGetDictInforService;

    @Autowired
    private JobsSupplierProduceDataRepository jobsSupplierProduceDataRepository;

    @Autowired
    private StbProducteInfoDataRepository stbProducteInfoDataRepository;

    @Autowired
    private StbProducteInfoTacRepository stbProducteInfoTacRepository;

    @Autowired
    private JobsSupplierProduceDataTacRepository jobsSupplierProduceDataTacRepository;

    @Autowired
    private CpeBoxupBillService cpeBoxupBillService;

    @Override
    public List<PilotProductReqVO> dataCheckIsOnly(PilotProductReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO) || CollectionUtils.isEmpty(reqDTO.getDataList())) {
            return Collections.emptyList();
        }
        //获取字典表数据
        Map<String, List<DictInfoForDTO>> dictMap = getDictMap();

        //1.参数校验
        this.checkParam(reqDTO, dictMap);
        //获取传入数据转化为map
        Map<String, PilotProductDTO> productDTOMap = reqDTO.getDataList().stream()
                .collect(Collectors.toMap(x -> CommonUtil.getString(x.getParamName()).toUpperCase(), Function.identity(), (a, b) -> a));

        //2.查询数据
        List<PilotProductParamVO> productAllList = processDictEntries(dictMap, productDTOMap, reqDTO.getType());

        //3.处理数据
        return getProductReqVOList(productAllList);
    }


    /**
     * 获取字典表数据
     *
     * @return
     */
    private Map<String, List<DictInfoForDTO>> getDictMap() {
        // 查询字段表数据
        Map<String, List<DictInfoForDTO>> dictMap = mesOmsGetDictInforService.getDictMap(
                Lists.newArrayList(Constant.LOOKUP_TYPE_8001040, Constant.LOOKUP_TYPE_8001050, Constant.LOOKUP_TYPE_8001032, Constant.LOOKUP_TYPE_8001033));
        //格式转化与原字表格式一致
        dictMap.forEach((a, b) -> {
            if (Constant.LOOKUP_TYPE_8001032.equals(a) || Constant.LOOKUP_TYPE_8001033.equals(a)) {
                b.forEach(x -> {
                    if (StringUtils.isEmpty(x.getDescription())) {
                        return;
                    }
                    String[] split = x.getDescription().split(Constant.HASH);
                    x.setLookupMeaning(split[NumConstant.NUM_ZERO]);
                    //是否为MAC,：1代表 去掉：去掉- 全部转大写
                    boolean macFlag = split.length > NumConstant.NUM_ONE && NumConstant.STR_ONE.equals(split[NumConstant.NUM_TWO]);
                    if (macFlag) {
                        x.setLookupMeaning(split[NumConstant.NUM_ZERO] + Constant.D_MAC);
                    }
                });
            }
        });
        return dictMap;
    }


    /**
     * 处理数据，返回结果
     *
     * @param productAllList
     * @return
     */
    private List<PilotProductReqVO> getProductReqVOList(List<PilotProductParamVO> productAllList) {
        //判断重复数据大于的需要返回
        Map<String, List<PilotProductParamVO>> paramMap = productAllList
                .stream()
                .collect(Collectors.groupingBy(PilotProductParamVO::getParamName));

        List<PilotProductReqVO> list = Lists.newArrayList();
        //组装参数返回
        paramMap.forEach((a, b) -> {
            Map<String, List<PilotProductParamVO>> sameMap = b.stream().collect(Collectors.groupingBy(x -> x.getParamName() + x.getEntityName() + x.getWorkUnit()));
            sameMap.forEach((s1, s2) -> {
                List<String> paramValueList = s2.stream().map(PilotProductParamVO::getParamValue).distinct().collect(Collectors.toList());
                PilotProductReqVO pilotProductVO = new PilotProductReqVO();
                BeanUtils.copyProperties(s2.get(NumConstant.NUM_ZERO), pilotProductVO);
                pilotProductVO.setSum(paramValueList.size());
                //返回1000
                pilotProductVO.setParamList(paramValueList.stream().limit(NumConstant.NUM_ONE_THOUSAND).collect(Collectors.toList()));
                list.add(pilotProductVO);
            });
        });
        return list;
    }

    private List<PilotProductParamVO> processDictEntries(Map<String, List<DictInfoForDTO>> dictMap,
                                                         Map<String, PilotProductDTO> productDTOMap,
                                                         String type) {
        List<PilotProductParamVO> productAllList = new ArrayList<>();

        //获取对应的字段表
        List<DictInfoForDTO> relevantEntries = getRelevantEntries(dictMap, type);
        for (DictInfoForDTO entry : relevantEntries) {
            String[] manList = entry.getLookupMeaning().split(Constant.COMMA);
            String strExcelFiled = manList.length > NumConstant.NUM_ONE ? manList[NumConstant.NUM_ZERO] : entry.getLookupMeaning();
            //是否为MAC
            boolean macFlag = manList.length > NumConstant.NUM_ONE && Constant.MAC.equals(manList[NumConstant.NUM_ONE]);
            //获取传参的参数
            PilotProductDTO pilotProductDTO = productDTOMap.get(strExcelFiled.toUpperCase());
            if (pilotProductDTO == null) {
                continue;
            }
            //查询满足条件的数据
            List<PilotProductParamVO> productSumList = fetchProductSumList(pilotProductDTO, macFlag, type, dictMap);
            //判断productSumList是否为空，不为空添加
            Optional.ofNullable(productSumList).ifPresent(productAllList::addAll);
        }
        return productAllList;
    }

    private List<DictInfoForDTO> getRelevantEntries(Map<String, List<DictInfoForDTO>> dictMap, String type) {
        switch (type) {
            case Constant.OEM:
                List<DictInfoForDTO> oemList = Lists.newArrayList();
                oemList.addAll(dictMap.getOrDefault(Constant.LOOKUP_TYPE_8001040, Collections.emptyList()));
                oemList.addAll(dictMap.getOrDefault(Constant.LOOKUP_TYPE_8001032, Collections.emptyList()));
                return oemList;
            case Constant.CPE:
                List<DictInfoForDTO> cpeList = Lists.newArrayList();
                cpeList.addAll(dictMap.getOrDefault(Constant.LOOKUP_TYPE_8001050, Collections.emptyList()));
                cpeList.addAll(dictMap.getOrDefault(Constant.LOOKUP_TYPE_8001033, Collections.emptyList()));
                return cpeList;
            default:
                return dictMap.values().stream()
                        .flatMap(java.util.Collection::stream)
                        .collect(Collectors.toList());
        }
    }

    private List<PilotProductParamVO> fetchProductSumList(PilotProductDTO pilotProductDTO, boolean macFlag, String type, Map<String, List<DictInfoForDTO>> dictMap) {
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        if (macFlag) {
            //查询MAC表数据
            return getParamMacList(pilotProductDTO, type, dictMap);
        } else {
            this.setParamStartEnd(pilotProductDTO);
            switch (type) {
                case Constant.OEM:
                    List<PilotProductParamVO> productSumOemAllList = Lists.newArrayList();
                    //oem 对应数据查询
                    this.oemDataAdd(pilotProductDTO, dictMap, productSumOemAllList);
                    this.oemTacAdd(pilotProductDTO, dictMap, productSumOemAllList);

                    return productSumOemAllList;
                case Constant.CPE:
                    //cpe对应数据查询
                    List<PilotProductParamVO> productSumCpeAllList = Lists.newArrayList();
                    this.cpeDataAdd(pilotProductDTO, dictMap, productSumCpeAllList);
                    this.cpeTacAdd(pilotProductDTO, dictMap, productSumCpeAllList);
                    return productSumCpeAllList;
                default:
                    //查询所有对应的表数据
                    List<PilotProductParamVO> productSumAllList = Lists.newArrayList();
                    this.oemDataAdd(pilotProductDTO, dictMap, productSumAllList);
                    this.oemTacAdd(pilotProductDTO, dictMap, productSumAllList);
                    this.cpeDataAdd(pilotProductDTO, dictMap, productSumAllList);
                    this.cpeTacAdd(pilotProductDTO, dictMap, productSumAllList);
                    return productSumAllList;

            }
        }
    }

    /**
     * cpe DATA 表类型查询
     */
    private void cpeDataAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productSumCpeAllList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001050).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumList = stbProducteInfoDataRepository.getProductSumCpeList(pilotProductDTO);
            Optional.ofNullable(productSumList).ifPresent(productSumCpeAllList::addAll);
        }
    }

    /**
     * oem DATA 表类型查询
     */
    private void oemDataAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productSumOemAllList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001040).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumList = jobsSupplierProduceDataRepository.getProductSumList(pilotProductDTO);
            Optional.ofNullable(productSumList).ifPresent(productSumOemAllList::addAll);
        }
    }

    /**
     * oem Tac类型查询
     */
    private void oemTacAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productSumAllList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001032).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumList = jobsSupplierProduceDataTacRepository.getProductSumList(pilotProductDTO);
            Optional.ofNullable(productSumList).ifPresent(productSumAllList::addAll);
        }
    }

    /**
     * cpe Tac表类型查询
     */
    private void cpeTacAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productSumAllList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001033).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumList = stbProducteInfoTacRepository.getProductSumCpeList(pilotProductDTO);
            Optional.ofNullable(productSumList).ifPresent(productSumAllList::addAll);
        }
    }

    /**
     * 查询 mac 表数据
     *
     * @param pilotProductDTO
     * @param type
     * @param dictMap
     * @return
     */
    private List<PilotProductParamVO> getParamMacList(PilotProductDTO pilotProductDTO, String type, Map<String, List<DictInfoForDTO>> dictMap) {
        //针对为mac 的数据需要替换-，：为空，然后大写 、统一大写校验
        //2、需要统一去掉分隔符“：”或“-”后进行校验
        List<String> paramList = pilotProductDTO.getParamList()
                .stream()
                .filter(StringUtils::isNotBlank)
                .map(x -> x.replace(Constant.HORIZON, Constant.EMPTY_NO_NO).replace(Constant.STRING_MAOHAO, Constant.EMPTY_NO_NO).toUpperCase()).collect(Collectors.toList());
        pilotProductDTO.setParamList(paramList);
        this.setParamStartEnd(pilotProductDTO);
        switch (type) {
            case Constant.OEM:
                List<PilotProductParamVO> productSumOemAllList = Lists.newArrayList();
                //查询oem MAC表数据
                this.oemDataMacAdd(pilotProductDTO, dictMap, productSumOemAllList);
                //查询oem TAC表数据
                this.oemTacAdd(pilotProductDTO, dictMap, productSumOemAllList);
                return productSumOemAllList;
            case Constant.CPE:
                List<PilotProductParamVO> productSumCpeAllList = Lists.newArrayList();
                //查询机顶盒 MAC表数据
                this.cpeDataMacAdd(pilotProductDTO, dictMap, productSumCpeAllList);
                //查询机顶盒 TAC表数据
                this.cpeTacAdd(pilotProductDTO, dictMap, productSumCpeAllList);
                return productSumCpeAllList;

            default:
                //查询所有表数据
                List<PilotProductParamVO> productAllSumMacList = Lists.newArrayList();
                this.oemDataMacAdd(pilotProductDTO, dictMap, productAllSumMacList);
                this.cpeDataMacAdd(pilotProductDTO, dictMap, productAllSumMacList);
                this.cpeTacAdd(pilotProductDTO, dictMap, productAllSumMacList);
                this.oemTacAdd(pilotProductDTO, dictMap, productAllSumMacList);
                return productAllSumMacList;
        }
    }

    private void cpeDataMacAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productAllSumMacList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001050).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumMacCpeList = stbProducteInfoDataRepository.getProductSumMacCpeList(pilotProductDTO);
            Optional.ofNullable(productSumMacCpeList).ifPresent(productAllSumMacList::addAll);
        }
    }

    private void oemDataMacAdd(PilotProductDTO pilotProductDTO, Map<String, List<DictInfoForDTO>> dictMap, List<PilotProductParamVO> productAllSumMacList) {
        if (getList(dictMap, Constant.LOOKUP_TYPE_8001040).contains(pilotProductDTO.getParamName().toUpperCase())) {
            List<PilotProductParamVO> productSumMacList = jobsSupplierProduceDataRepository.getProductSumMacList(pilotProductDTO);
            Optional.ofNullable(productSumMacList).ifPresent(productAllSumMacList::addAll);
        }
    }

    private List<String> getList(Map<String, List<DictInfoForDTO>> dictMap, String lookupType) {
        return dictMap.getOrDefault(lookupType, Lists.newArrayList())
                .stream()
                .map(infoForDTO -> infoForDTO.getLookupMeaning().replace(Constant.D_MAC, Constant.EMPTY_NO_NO).toUpperCase()).collect(Collectors.toList());
    }

    private void setParamStartEnd(PilotProductDTO pilotProductDTO) {
        //针对类型为数据段是根据范围来区分
        if (StringUtils.equals(pilotProductDTO.getParamType(), NumConstant.STR_ONE) && pilotProductDTO.getParamList().size() > 1) {
            pilotProductDTO.setParamStart(pilotProductDTO.getParamList().get(NumConstant.NUM_ZERO));
            pilotProductDTO.setParamEnd(pilotProductDTO.getParamList().get(NumConstant.NUM_ONE));
        }
    }


    /**
     * 入参校验
     *
     * @param reqDTO
     * @param dictMap
     */
    private void checkParam(PilotProductReqDTO reqDTO, Map<String, List<DictInfoForDTO>> dictMap) {
        //传值为null赋值空字符串
        reqDTO.setType(Objects.isNull(reqDTO.getType()) ? Constant.EMPTY_NO_NO : reqDTO.getType());
        //校验类型
        this.checkType(reqDTO);
        // 传参校验
        reqDTO.getDataList().forEach(x -> this.paramProcess(x, dictMap, getLookupType(reqDTO.getType())));
        //校验参数是否重复
        this.checkParamName(reqDTO.getDataList());
    }

    /**
     * 参数类型校验
     *
     * @param reqDTO
     */
    private void checkType(PilotProductReqDTO reqDTO) {
        //只能为OEM，机顶盒 ，空字符串
        List<String> typeList = Lists.newArrayList(Constant.OEM, Constant.CPE, Constant.STRING_EMPTY);
        if (!typeList.contains(reqDTO.getType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXIST_NOT_PARAM_TYPE);
        }
    }

    /**
     * 参数名称校验
     *
     * @param dataList
     */
    private void checkParamName(List<PilotProductDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        long count = dataList.stream().filter(x -> StringUtils.isNotBlank(x.getParamName())).map(x -> x.getParamName().toUpperCase()).distinct().count();
        if (dataList.size() != count) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_NAME_REPEATED);
        }
    }


    private List<String> getLookupType(String type) {
        switch (type) {
            //DHOME 对应的字段
            case Constant.OEM:
                return Lists.newArrayList(Constant.LOOKUP_TYPE_8001040, Constant.LOOKUP_TYPE_8001032);
            //DHOME CPE
            case Constant.CPE:
                return Lists.newArrayList(Constant.LOOKUP_TYPE_8001050, Constant.LOOKUP_TYPE_8001033);
            default:
                return null;
        }
    }

    private void paramProcess(PilotProductDTO productDTO, Map<String, List<DictInfoForDTO>> dictMap, List<String> lookupList) {
        //类型为空的两个都要校验是否字段存在于字段表中
        List<DictInfoForDTO> dtoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(lookupList)) {
            dtoList = dictMap.values().stream()
                    .flatMap(java.util.Collection::stream)
                    .collect(Collectors.toList());
        } else {
            for (String lookup : lookupList) {
                dtoList.addAll(dictMap.getOrDefault(lookup, Lists.newArrayList()));
            }
        }
        this.paramContrast(productDTO, dtoList);
    }

    /**
     * 校验
     *
     * @param productDTO
     * @param dictInfoForDTOS
     */
    private void paramContrast(PilotProductDTO productDTO, List<DictInfoForDTO> dictInfoForDTOS) {
        //去除,MAC，获取字段表对应的字段
        Set<String> paramSet = dictInfoForDTOS.stream()
                .map(infoForDTO -> infoForDTO.getLookupMeaning().replace(Constant.D_MAC, Constant.EMPTY_NO_NO).toUpperCase())
                .collect(Collectors.toSet());
        // 参数不存在
        if (StringUtils.isEmpty(productDTO.getParamName()) || !paramSet.contains(productDTO.getParamName().toUpperCase())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXIST_NOT_PARAM_NAME);
        }
        //值不能为空
        if (CollectionUtils.isEmpty(productDTO.getParamList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_LIST_IS_NULL);
        }
        // 参数类型为数据段时，paramList列表长度必须为2
        if (StringUtils.equals(productDTO.getParamType(), NumConstant.STR_ONE) && productDTO.getParamList().size() != NumConstant.NUM_TWO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DISCRETE_PARAM_NAME_ERROR);
        }

        //类型不能为空
        if (StringUtils.isEmpty(productDTO.getParamType()) || !Lists.newArrayList(NumConstant.STR_2, NumConstant.STR_ONE).contains(productDTO.getParamType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_TYPE_IS_INCORRECT);
        }
    }

    /**
     * @param snStartList  SN特征值
     * @param daysAgoStart 查询开始日期，当前日期 daysAgoStart 天之前
     * @param daysAgoEnd   查询结束日期，当前日期 daysAgoEnd 天之前
     * @return Map<SN特征值, 发货数量>
     */
    @Override
    public Map<String, Integer> getCpeSnListCount(List<String> snStartList, Integer daysAgoStart, Integer daysAgoEnd) {
        if (CollectionUtils.isEmpty(snStartList)) {
            return new HashMap<>();
        }
        if (daysAgoStart == null) {
            daysAgoStart = 1;
        }
        if (daysAgoEnd == null) {
            daysAgoEnd = 0;
        }
        List<String> collect = snStartList
                .stream()
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<StbProductSnCount> snCountList = jobsSupplierProduceDataRepository.getCpeSnCount(collect, daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isEmpty(snCountList)) {
            snCountList = new ArrayList<>();
        }
        Map<String, Integer> map = snCountList.stream().collect(Collectors.toMap(StbProductSnCount::getSnStart, StbProductSnCount::getCount));
        List<String> itemBarcodes = cpeBoxupBillService.getItemBarcodesDaysAgo(daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isEmpty(itemBarcodes)) {
            return map;
        }
        List<List<String>> itemBarcodeList = CommonUtils.splitList(itemBarcodes, NumConstant.NUM_ONE_THOUSAND);
        List<StbProductSnCount> snCountByItemBarcodes = new ArrayList<>();
        //切数据源
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        for (List<String> itemList : itemBarcodeList) {
            snCountByItemBarcodes.addAll(jobsSupplierProduceDataRepository.getCpeSnCountByItemBarcodes(collect, itemList));
        }
        if (CollectionUtils.isEmpty(snCountByItemBarcodes)) {
            return map;
        }
        for (StbProductSnCount snCountByItemBarcode : snCountByItemBarcodes) {
            map.put(snCountByItemBarcode.getSnStart(),
                    map.getOrDefault(snCountByItemBarcode.getSnStart(), 0) + snCountByItemBarcode.getCount());
        }
        return map;
    }

    @Override
    public List<String> getCpeSnListBySnStart(String snStart, Integer daysAgoStart, Integer daysAgoEnd) {
        if (StringUtils.isBlank(snStart)) {
            return Collections.emptyList();
        }
        if (daysAgoStart == null) {
            daysAgoStart = 1;
        }
        if (daysAgoEnd == null) {
            daysAgoEnd = 0;
        }
        List<String> snList = jobsSupplierProduceDataRepository.getCpeSnListBySnStart(snStart, daysAgoStart,
                daysAgoEnd);
        if (CollectionUtils.isEmpty(snList)) {
            snList = new ArrayList<>();
        }

        List<String> itemBarcodes = cpeBoxupBillService.getItemBarcodesDaysAgo(daysAgoStart, daysAgoEnd);
        if (CollectionUtils.isNotEmpty(itemBarcodes)) {
            List<List<String>> itemBarcodeList = CommonUtils.splitList(itemBarcodes, NumConstant.NUM_ONE_THOUSAND);
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
            for (List<String> itemList : itemBarcodeList) {
                snList.addAll(jobsSupplierProduceDataRepository.getCpeSnListByItemBarcodes(snStart, itemList));
            }
        }
        return snList.stream().distinct().collect(Collectors.toList());
    }
}
