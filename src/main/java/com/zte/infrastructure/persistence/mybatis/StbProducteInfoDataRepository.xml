<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.StbProducteInfoDataRepository">
	<!-- 机顶盒发货信息查询（部分字段） -->
	<resultMap id="StbResultMap" type="com.zte.domain.model.datawb.StbProdDeliveryInfoQueryExtend">
		<result column="PRODUCTION_UNIT" jdbcType="VARCHAR" property="productionUnit" />
		<result column="PRODUCTION_DATE" jdbcType="TIMESTAMP" property="productionDate" />
		<result column="BRAND" jdbcType="VARCHAR" property="brand" />
		<result column="DEVICE_TYPE" jdbcType="VARCHAR" property="deviceType" />
		<result column="MODEL" jdbcType="VARCHAR" property="model" />
		<result column="PRODUCTION_ENTITYNO" jdbcType="VARCHAR" property="productionEntityno" />
		<result column="PRODUCE_BILLNO" jdbcType="VARCHAR" property="produceBillno" />
		<result column="MAC1_ADDR" jdbcType="VARCHAR" property="mac1Addr" />
		<result column="MAC2_ADDR" jdbcType="VARCHAR" property="mac2Addr" />
		<result column="BOARD_SN1" jdbcType="VARCHAR" property="boardSn1" />
		<result column="BOARD_SN2" jdbcType="VARCHAR" property="boardSn2" />
		<result column="PRODUCT_SN" jdbcType="VARCHAR" property="productSn" />
		<result column="CARTON_SN" jdbcType="VARCHAR" property="cartonSn" />
		<result column="PALLET_SN" jdbcType="VARCHAR" property="palletSn" />
		<result column="POWERSOURCE_SN" jdbcType="VARCHAR" property="powersourceSn" />
		<result column="REMOTECONTROL_SN" jdbcType="VARCHAR" property="remotecontrolSn" />
		<result column="TEST_RESULT" jdbcType="VARCHAR" property="testResult" />
		<result column="TEST_TIME" jdbcType="VARCHAR" property="testTime" />
		<result column="AGEING_TESTRESULT" jdbcType="VARCHAR" property="ageingTestresult" />
		<result column="AGEING_TESTTIME" jdbcType="VARCHAR" property="ageingTesttime" />
		<result column="MAC_CONFIGRESULT" jdbcType="VARCHAR" property="macConfigresult" />
		<result column="MAC_CONFIGTIME" jdbcType="VARCHAR" property="macConfigtime" />
		<result column="ONOFF_TESTRESULT" jdbcType="VARCHAR" property="onoffTestresult" />
		<result column="ONOFF_TESTTIME" jdbcType="VARCHAR" property="onoffTesttime" />
		<result column="MACHINE_TESTRESULT" jdbcType="VARCHAR" property="machineTestresult" />
		<result column="MACHINE_TESTTIME" jdbcType="VARCHAR" property="machineTesttime" />
		<result column="MACHINE_CHECKRESULT" jdbcType="VARCHAR" property="machineCheckresult" />
		<result column="MACHINE_CHECKTIME" jdbcType="VARCHAR" property="machineChecktime" />
		<result column="FAC_CONFIGRESULT" jdbcType="VARCHAR" property="facConfigresult" />
		<result column="FAC_CONFIGTIME" jdbcType="VARCHAR" property="facConfigtime" />
		<result column="SOFT_VER_NUM" jdbcType="VARCHAR" property="softVerNum" />
		<result column="LOGO_VER_NUM" jdbcType="VARCHAR" property="logoVerNum" />
		<result column="FAC_CONFIGFILE" jdbcType="VARCHAR" property="facConfigfile" />
		<result column="REMARK1" jdbcType="VARCHAR" property="remark1" />
		<result column="REMARK2" jdbcType="VARCHAR" property="remark2" />
		<result column="REMARK3" jdbcType="VARCHAR" property="remark3" />
		<result column="REMARK4" jdbcType="VARCHAR" property="remark4" />
		<result column="REMARK5" jdbcType="VARCHAR" property="remark5" />
		<result column="REMARK6" jdbcType="VARCHAR" property="remark6" />
		<result column="REMARK7" jdbcType="VARCHAR" property="remark7" />
		<result column="CREATED_FULL_BY" jdbcType="VARCHAR" property="createdFullBy" />
		<result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
		<result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
		<result column="NET_ACCESS_SIGN_NUM" jdbcType="VARCHAR" property="netAccessSignNum" />
		<result column="SCRAMBLE_CODE" jdbcType="VARCHAR" property="scrambleCode" />
		<result column="NASN" jdbcType="VARCHAR" property="nasn" />
		<result column="MADEIN" jdbcType="VARCHAR" property="madeIn" />
	</resultMap>

	<!-- 机顶盒发货信息查询（部分字段） -->
	<select id="getListStbProdDeliveryInfo" parameterType="java.lang.String" resultMap="StbResultMap">   
	    SELECT
			PRODUCTION_UNIT, PRODUCTION_DATE, BRAND, DEVICE_TYPE, MODEL, 
			PRODUCTION_ENTITYNO, PRODUCE_BILLNO, MAC1_ADDR, MAC2_ADDR, BOARD_SN1, 
			BOARD_SN2, PRODUCT_SN, CARTON_SN, PALLET_SN, POWERSOURCE_SN, 
			REMOTECONTROL_SN, TEST_RESULT, TEST_TIME, AGEING_TESTRESULT, AGEING_TESTTIME, 
			MAC_CONFIGRESULT, MAC_CONFIGTIME, ONOFF_TESTRESULT, ONOFF_TESTTIME, MACHINE_TESTRESULT, 
			MACHINE_TESTTIME, MACHINE_CHECKRESULT, MACHINE_CHECKTIME, FAC_CONFIGRESULT, FAC_CONFIGTIME, 
			SOFT_VER_NUM, LOGO_VER_NUM, FAC_CONFIGFILE, REMARK1, REMARK2, 
			REMARK3, REMARK4, REMARK5, REMARK6, REMARK7, 
			CREATED_FULL_BY, CREATION_DATE, LAST_UPDATE_DATE, NET_ACCESS_SIGN_NUM, SCRAMBLE_CODE, NASN, MADEIN
	    FROM STB_PRODUCTE_INFO t
	    WHERE t.ENABLED_FLAG = 'Y'
	        AND t.PRODUCT_SN in (${barcodes})
    </select>
	<select id="getProductSumCpeList" resultType="com.zte.interfaces.dto.sfc.PilotProductParamVO">
		select
		PRODUCTION_ENTITYNO entityName ,
		PRODUCTION_UNIT workUnit,
		#{paramName} as paramName,
		${paramName} as paramValue
		from SFC.STB_PRODUCTE_INFO  t
		where
		t.ENABLED_FLAG = 'Y'
		<if test='paramType =="2"'>
			and ${paramName}  in
			<foreach collection="paramList" item="item" separator=','
					 open='(' close=')' index="index">
				#{item}
			</foreach>
		</if>
		<if test='paramType =="1"'>
			and  ${paramName}   between #{paramStart} and  #{paramEnd}
		</if>
	</select>

	<select id="getProductSumMacCpeList" resultType="com.zte.interfaces.dto.sfc.PilotProductParamVO">
		select
		PRODUCTION_ENTITYNO entityName ,
		PRODUCTION_UNIT workUnit,
		#{paramName} as paramName,
		tm.${paramName} as paramValue
		from SFC.STB_PRODUCTE_INFO_MAC  tm
		JOIN SFC.STB_PRODUCTE_INFO t  on tm. RECORD_ID  = t.RECORD_ID
		where
		t.ENABLED_FLAG = 'Y'
		<if test='paramType =="2"'>
			and tm.${paramName}  in
			<foreach collection="paramList" item="item" separator=','
					 open='(' close=')' index="index">
				#{item}
			</foreach>
		</if>
		<if test='paramType =="1"'>
			and  tm.${paramName}  between #{paramStart} and  #{paramEnd}
		</if>
	</select>

	<!-- 机顶盒发货数量查询 -->
	<select id="getDhomeSnCount" parameterType="java.util.List" resultType="com.zte.domain.model.datawb.StbProductSnCount">
		SELECT
		T4.snStart,
		COUNT(1) AS count
		FROM (
		<foreach item="snStart" collection="snStartList" separator=" UNION ALL ">
			SELECT #{snStart,jdbcType=VARCHAR} snStart from dual
		</foreach>
		) T4
		JOIN SFC.STB_PRODUCTE_INFO_TAC T1 ON (T1.EXTSN LIKE T4.snStart || '%' OR T1.SN9KEY LIKE T4.snStart || '%')
		JOIN SFC.STB_PRODUCTE_INFO T2 ON T1.RECORD_ID = T2.RECORD_ID
		JOIN SFC.PKG_SUBMAC_SCAN_INFO_V T3 ON T2.PRODUCT_SN = T3.item_barcode
		WHERE T1.ENABLED_FLAG = 'Y'
		AND T2.ENABLED_FLAG = 'Y'
		AND T3.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
		AND T3.ACTION_DATE &lt;  TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
		GROUP BY T4.snStart
	</select>
	<select id="getDhomeSnCountByProductSnList" parameterType="java.util.Map" resultType="com.zte.domain.model.datawb.StbProductSnCount">
		SELECT
		T4.snStart,
		COUNT(1) AS count
		FROM (
		<foreach item="snStart" collection="snStartList" separator=" UNION ALL ">
			SELECT #{snStart,jdbcType=VARCHAR} snStart from dual
		</foreach>
		) T4
		JOIN SFC.STB_PRODUCTE_INFO_TAC T1 ON (T1.EXTSN LIKE T4.snStart || '%' OR T1.SN9KEY LIKE T4.snStart || '%')
		JOIN SFC.STB_PRODUCTE_INFO T2 ON T1.RECORD_ID = T2.RECORD_ID
		WHERE T1.ENABLED_FLAG = 'Y'
		AND T2.ENABLED_FLAG = 'Y'
		AND T2.PRODUCT_SN IN
		<foreach collection="productSnList" item="item" separator=',' open='(' close=')'>
			#{item,jdbcType=VARCHAR}
		</foreach>
		GROUP BY T4.snStart
	</select>

	<select id="getDhomeSnListBySnStart" resultType="java.lang.String">
		SELECT T1.SN
		FROM (
			SELECT EXTSN AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE EXTSN LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
			UNION ALL
			SELECT SN9KEY AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE SN9KEY LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
		) T1
		JOIN SFC.STB_PRODUCTE_INFO T2 ON T1.RECORD_ID = T2.RECORD_ID
		JOIN sfc.pkg_submac_scan_info scan_info ON T2.PRODUCT_SN = scan_info.item_barcode
		JOIN sfc.wsm_contract_machine_info con_mac ON con_mac.submachine_id = scan_info.submachine_id
		left JOIN sfc.cpm_boxup_bills_scan box_bill ON scan_info.bill_id = box_bill.bill_id AND box_bill.scan_flag = 105
		WHERE
		T2.ENABLED_FLAG = 'Y'
		AND scan_info.enabled_flag = 'Y'
		AND con_mac.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
		AND con_mac.ACTION_DATE &lt; TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
		UNION ALL
		SELECT T1.SN
		FROM (
			SELECT EXTSN AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE EXTSN LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
			UNION ALL
			SELECT SN9KEY AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE SN9KEY LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
		) T1
		JOIN SFC.STB_PRODUCTE_INFO T2 ON T1.RECORD_ID = T2.RECORD_ID
		JOIN
		( SELECT
		DECODE (a.item_series, NULL, a.item_barcode, a.item_series ) item_barcode , a.wip_entity_name, a.item_id
		FROM sfc.wsm_complement_scan_info a where a.enabled_flag = 'Y') wcsi
		ON T2.PRODUCT_SN = wcsi.item_barcode
		JOIN sfc.wsm_contract_machine_info_v con_mac ON wcsi.wip_entity_name = con_mac.submachine_number
		JOIN sfc.bas_items_info e ON wcsi.item_id = e.item_id
		WHERE
		T2.ENABLED_FLAG = 'Y'
		AND e.enabled_flag = 'Y'
		AND e.organization_id = 1
		AND con_mac.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
		AND con_mac.ACTION_DATE &lt; TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
	</select>

	<select id="getDhomeSnListByItemList" resultType="java.lang.String">
		SELECT T1.SN
		FROM (
			SELECT EXTSN AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE EXTSN LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
			UNION ALL
			SELECT SN9KEY AS SN, RECORD_ID
			FROM STB_PRODUCTE_INFO_TAC
			WHERE SN9KEY LIKE #{snStart,jdbcType=VARCHAR} || '%'
			AND ENABLED_FLAG = 'Y'
		) T1
		JOIN SFC.STB_PRODUCTE_INFO T2 ON T1.RECORD_ID = T2.RECORD_ID
		WHERE T2.ENABLED_FLAG = 'Y'
		AND T2.PRODUCT_SN IN
		<foreach collection="itemBarcodes" item="item" separator=',' open='(' close=')'>
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
</mapper>