<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.datawb.MeiTuanRepository">
    <!-- Started by AICoder, pid:pbd08g58048abfc14f2e0ab1d177de3c0cf48a32 -->
    <select id="getProductOID" resultType="java.lang.String" parameterType="com.zte.interfaces.dto.OIDDto">
        select distinct es.MEMO
        from WMES.CPM_CONTRACT_MFG_SITES es,
        wmes.CPM_CONTRACT_ENTITIES CCE,
        wmes.WERP_CON_ENTITY_TRACE wcet
        where es.ENTITY_ID = cce.entity_id
        and wcet.entity_id = es.entity_id
        and wcet.last_update_date  >= add_months(TRUNC(SYSDATE),-3)
        and es.MEMO is not null
        and instr(cce.entity_name,#{entityName,jdbcType=VARCHAR})>0
        and cce.enabled_flag='Y'
        and instr(cce.user_address,#{userAddress,jdbcType=VARCHAR})>0
        AND wcet.status_name != '发货'
    </select>
    <select id="getProductDataUpload" resultType="com.zte.interfaces.dto.MTProductDataUploadLogDTO" parameterType="com.zte.interfaces.dto.OIDDto">
        SELECT CCE.ENTITY_NAME produceWoCode,
        1 produceCount,
        CASE  WHEN instr(CCMS.MEMO,'_')>0
        THEN SUBSTR(CCMS.MEMO, 1,instr(CCMS.MEMO,'_')-1)
        ELSE CCMS.MEMO  END itemCode,
        CASE  WHEN REGEXP_SUBSTR(CCMS.MEMO, '_([0-9]+)',1,1,'i',1) IS NOT null
        THEN cast(REGEXP_SUBSTR(CCMS.MEMO, '_([0-9]+)',1,1,'i',1) AS number)
        ELSE -1 END itemTotal,
        TO_CHAR(S.WILL_PRODUCT_DATE, 'yyyy-mm-dd hh24:mi:ss') preparingTime,
        wdj.SCHEDULED_COMPLETION_DATE estimatedWarehousingTime,
        S.ASSEMBLY_REAL_BEGIN_DATE pullMaterialComplateTime,
        S.DEBUG_REAL_BEGIN_DATE assemblyComplateTime,
        S.CHECK_REAL_BEGIN_DATE testComplateTime,
        HIS.CREATION_DATE warehousingComplateTime,
        S.LAST_UPDATE_DATE lastUpdateDate
        FROM WMES.CPM_CONTRACT_ENTITIES CCE,
        WMES.CPM_CONTRACT_MFG_SITES CCMS,
        WMES.WERP_CON_ENTITY_TRACE S,
        WMES.WIP_DISCRETE_JOBS WDJ,
        (SELECT ENTITY_ID, MAX(CREATION_DATE) CREATION_DATE
        FROM WMES.WMES_ENTITY_TRACE_MODIFY_HIS
        WHERE ENABLED_FLAG = 'Y'
        AND NEW_SEQ_STATUS = '入库'
        GROUP BY ENTITY_ID) HIS
        WHERE CCE.ENABLED_FLAG(+) = 'Y'
        AND HIS.ENTITY_ID(+) = CCE.ENTITY_ID
        AND CCMS.ENTITY_ID = CCE.ENTITY_ID
        AND CCE.ENTITY_ID = S.ENTITY_ID
        AND CCE.WIP_ENTITY_ID = WDJ.WIP_ENTITY_ID(+)
        AND CCE.ORGANIZATION_ID = WDJ.ORGANIZATION_ID(+)
        AND CCMS.MEMO IN (
        <foreach collection="oidList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
        AND INSTR(CCE.ENTITY_NAME, #{entityName,jdbcType=VARCHAR}) > 0
        AND INSTR(CCE.USER_ADDRESS, #{userAddress,jdbcType=VARCHAR}) > 0
        ORDER BY S.LAST_UPDATE_DATE ASC
    </select>
    <select id="getProductDataStatus" resultType="com.zte.interfaces.dto.MtProductDataStatusDTO" parameterType="com.zte.interfaces.dto.OIDDto">
        SELECT memo,status,complateCount,totalCount,CREATION_DATE createDate,LAST_UPDATE_DATE lastUpdateDate,0 isAdd
        FROM WMES.meituan_productdatastatus
        WHERE memo IN (
        <foreach collection="oidList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
    </select>
    <select id="getEntitiesStatus" resultType="com.zte.interfaces.dto.MTEntitiesStatusDTO" parameterType="com.zte.interfaces.dto.OIDDto">
        SELECT CCMS.MEMO itemCode,CCE.ENTITY_NAME produceWoCode,
        (SELECT MAX(CREATION_DATE)  FROM WMES.WMES_ENTITY_TRACE_MODIFY_HIS
        WHERE ENABLED_FLAG = 'Y'
        AND NEW_SEQ_STATUS = '入库' AND ENTITY_ID = CCMS.ENTITY_ID) RKDATE,
        (SELECT MAX(CREATION_DATE)  FROM WMES.WMES_ENTITY_TRACE_MODIFY_HIS
        WHERE ENABLED_FLAG = 'Y'
        AND NEW_SEQ_STATUS = '退库' AND ENTITY_ID = CCMS.ENTITY_ID) TKDATE
        FROM WMES.CPM_CONTRACT_MFG_SITES CCMS
        INNER JOIN WMES.CPM_CONTRACT_ENTITIES CCE ON CCMS.ENTITY_ID = CCE.ENTITY_ID
        LEFT JOIN WMES.WMES_ENTITY_TRACE_MODIFY_HIS HIS ON CCMS.ENTITY_ID = HIS.ENTITY_ID
        WHERE CCMS.ENABLED_FLAG='Y' AND CCMS.MEMO = #{oid,jdbcType=VARCHAR}
        GROUP BY CCMS.MEMO,CCE.ENTITY_NAME,CCMS.ENTITY_ID
    </select>
    <select id="getHistoryProductDataUpload" resultType="com.zte.interfaces.dto.MTProductDataUploadLogDTO" parameterType="com.zte.interfaces.dto.OIDDto">
        SELECT itemCode,
        produceWoCode,
        produceCount,
        preparingTime,
        estimatedWarehousingTime,
        pullMaterialComplateTime,
        assemblyComplateTime,
        testComplateTime,
        warehousingComplateTime,
        createtime as lastUpdateDate
        FROM wmes.meituan_productdataupload
        WHERE itemCode IN (
        <foreach collection="oidList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
    </select>
    <insert id="insertHistoryProductDataUpload" parameterType="java.util.List">
        insert into wmes.meituan_productdataupload(itemcode,producewocode,producecount,preparingtime,estimatedwarehousingtime,
        pullmaterialcomplatetime,assemblycomplatetime,testcomplatetime,warehousingcomplatetime,createtime
        )select itemcode,producewocode,producecount,preparingtime,estimatedwarehousingtime,
        pullmaterialcomplatetime,assemblycomplatetime,testcomplatetime,warehousingcomplatetime,createtime
        from (
        <foreach collection="list" separator=" union all " item="item">
            select
            #{item.itemCode,jdbcType=VARCHAR} itemcode,
            #{item.produceWoCode,jdbcType=VARCHAR} producewocode,
            #{item.produceCount,jdbcType=INTEGER} producecount,
            #{item.preparingTime,jdbcType=VARCHAR} preparingtime,
            #{item.estimatedWarehousingTime,jdbcType=VARCHAR} estimatedwarehousingtime,
            #{item.pullMaterialComplateTime,jdbcType=VARCHAR} pullmaterialcomplatetime,
            #{item.assemblyComplateTime,jdbcType=VARCHAR} assemblycomplatetime,
            #{item.testComplateTime,jdbcType=VARCHAR} testcomplatetime,
            #{item.warehousingComplateTime,jdbcType=VARCHAR} warehousingcomplatetime,
            sysdate createtime
            from dual
        </foreach>
        ) t
    </insert>
    <update id="batchUpdateHistoryProductDataUpload" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update wmes.meituan_productdataupload t set
            t.produceCount=#{item.produceCount,jdbcType=INTEGER},
            t.preparingtime= #{item.preparingTime,jdbcType=VARCHAR},
            t.estimatedwarehousingtime= #{item.estimatedWarehousingTime,jdbcType=VARCHAR},
            t.pullmaterialcomplatetime= #{item.pullMaterialComplateTime,jdbcType=VARCHAR},
            t.assemblycomplatetime= #{item.assemblyComplateTime,jdbcType=VARCHAR},
            t.testcomplatetime=#{item.testComplateTime,jdbcType=VARCHAR},
            t.warehousingcomplatetime=#{item.warehousingComplateTime,jdbcType=VARCHAR},
            t.createtime=sysdate
            where  t.itemcode= #{item.itemCode,jdbcType=VARCHAR}
            and t.produceWoCode= #{item.produceWoCode,jdbcType=VARCHAR}
        </foreach>
    </update>

    <insert id="insertMtProductDataStatusDTO" parameterType="java.util.List">
        INSERT INTO WMES.MEITUAN_PRODUCTDATASTATUS
          (MEMO,
           STATUS,
           PRE_STATUS,
           COMPLATECOUNT,
           TOTALCOUNT,
           CREATION_DATE,
           LAST_UPDATE_DATE)
          SELECT MEMO,
                 STATUS,
                 PRE_STATUS,
                 COMPLATECOUNT,
                 TOTALCOUNT,
                 CREATION_DATE,
                 LAST_UPDATE_DATE
            FROM (
        <foreach collection="list" separator=" union all " item="item">
            select
            #{item.memo,jdbcType=VARCHAR} MEMO,
            #{item.status,jdbcType=VARCHAR} STATUS,
            #{item.preStatus,jdbcType=VARCHAR} PRE_STATUS,
            #{item.complateCount,jdbcType=INTEGER} COMPLATECOUNT,
            #{item.totalCount,jdbcType=INTEGER} TOTALCOUNT,
            sysdate CREATION_DATE,
            sysdate LAST_UPDATE_DATE
            from dual
        </foreach>
        ) t
    </insert>
    <update id="batchUpdateProductDataStatusDTO" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update WMES.MEITUAN_PRODUCTDATASTATUS t
            <set>
                <if test="item.status != null and item.status != ''">
                    t.status=#{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.preStatus != null and item.preStatus != ''">
                    t.pre_status=#{item.preStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.preToS != null and item.preToS != '' and item.preToS == 1">
                    t.status=t.pre_status,
                </if>
                <if test="item.complateCount != null and item.complateCount != ''">
                    t.complateCount= #{item.complateCount,jdbcType=INTEGER},
                </if>
                <if test="item.totalCount != null and item.totalCount != ''">
                    t.totalCount= #{item.totalCount,jdbcType=INTEGER},
                </if>
                t.LAST_UPDATE_DATE=sysdate
            </set>
            <where>
                t.memo= #{item.memo,jdbcType=VARCHAR}
                <if test="item.preToS != null and item.preToS != '' and item.preToS == 1">
                    and t.pre_status is not null
                </if>
            </where>
        </foreach>
    </update>
    <!-- Ended by AICoder, pid:pbd08g58048abfc14f2e0ab1d177de3c0cf48a32 -->
</mapper>