<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.WsmAssembleLinesRepository">
  <resultMap id="ResultMap" type="com.zte.domain.model.WsmAssembleLinesWriteBack">
    <id column="ASSEMBLE_LINES_ID" jdbcType="DECIMAL" property="assembleLinesId" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_LOGIN" jdbcType="DECIMAL" property="lastUpdateLogin" />
    <result column="ASSEMBLE_HEADERS_ID" jdbcType="DECIMAL" property="assembleHeadersId" />
    <result column="SCAN_TYPE" jdbcType="VARCHAR" property="scanType" />
    <result column="ITEM_BARCODE" jdbcType="VARCHAR" property="itemBarcode" />
    <result column="ITEM_ID" jdbcType="DECIMAL" property="itemId" />
    <result column="ITEM_QTY" jdbcType="DECIMAL" property="itemQty" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_VERSION" jdbcType="VARCHAR" property="itemVersion" />
    <result column="ITEM_SERIES" jdbcType="VARCHAR" property="itemSeries" />
    <result column="POSITION_INFO" jdbcType="VARCHAR" property="positionInfo" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="PCB_VERSION" jdbcType="VARCHAR" property="pcbVersion" />
    <result column="SOFTWARE" jdbcType="VARCHAR" property="software" />
    <result column="HARDLOGIC" jdbcType="VARCHAR" property="hardlogic" />
    <result column="EXCEL_IMPORT_FLAG" jdbcType="VARCHAR" property="excelImportFlag" />
    <result column="EXCEL_ASSEMBLE_NAME" jdbcType="VARCHAR" property="excelAssembleName" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="VERIFY_FLAG" jdbcType="VARCHAR" property="verifyFlag" />
  </resultMap>
  
    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.WsmAssembleLines">
    <result column="MAINNAME" jdbcType="VARCHAR" property="mainName" />
    <result column="MAINBARCODE" jdbcType="VARCHAR" property="mainBarcode" />
    <result column="MAIN_ITEMCODE" jdbcType="VARCHAR" property="mainItemCode" />
    <result column="MAIN_VERSION" jdbcType="VARCHAR" property="mainVersion" />
    <result column="SUB_ITEM_CODE" jdbcType="VARCHAR" property="subItemCode" />
    <result column="SUB_ITEM_BARCODE" jdbcType="VARCHAR" property="subItemBarcode" />
    <result column="SUB_ITEM_VERSION" jdbcType="VARCHAR" property="subItemVersion" />
    <result column="SUB_ITEM_SERIES" jdbcType="VARCHAR" property="subItemSeries" />
    <result column="SUB_ITEM_NAME" jdbcType="VARCHAR" property="subItemName" />
    <result column="SUB_ITEM_QTY" jdbcType="DECIMAL" property="subItemQty" />
    <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType" />
    <result column="SITE_ADDRESS" jdbcType="VARCHAR" property="siteAddress" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="USER_CNAME" jdbcType="VARCHAR" property="userCname" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="SUB_PCB_VERSION" jdbcType="VARCHAR" property="subPcbVersion" />
    <result column="SUB_SOFTWARE" jdbcType="VARCHAR" property="subSoftware" />
    <result column="SUB_HARDLOGIC" jdbcType="VARCHAR" property="subHardlogic" />
    <result column="MAIN_PCB_VERSION" jdbcType="VARCHAR" property="mainPcbVersion" />
    <result column="MAIN_SOFTWARE" jdbcType="VARCHAR" property="mainSoftware" />
    <result column="MAIN_HARDLOGIC" jdbcType="VARCHAR" property="mainHardlogic" />
  </resultMap>

    <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.WsmAssembleLinesDTO">
        <result column="ASSEMBLE_HEADERS_ID" jdbcType="VARCHAR" property="assembleHeadersId" />
        <result column="MAIN_ITEM_BARCODE" jdbcType="VARCHAR" property="mainItemBarcode" />
        <result column="MAIN_ITEM_CODE" jdbcType="VARCHAR" property="mainItemCode" />
        <result column="MAIN_ITEM_NAME" jdbcType="VARCHAR" property="mainItemName" />
        <result column="MAIN_ITEM_VERSION" jdbcType="VARCHAR" property="mainItemVersion" />
        <result column="SUB_ITEM_BARCODE" jdbcType="VARCHAR" property="subItemBarcode" />
        <result column="SUB_ITEM_CODE" jdbcType="VARCHAR" property="subItemCode" />
        <result column="SUB_ITEM_NAME" jdbcType="VARCHAR" property="subItemName" />
        <result column="SUB_ITEM_VERSION" jdbcType="VARCHAR" property="subItemVersion" />
        <result column="SUB_ITEM_SERIES" jdbcType="VARCHAR" property="subItemSeries" />
        <result column="SUB_ITEM_QTY" jdbcType="DECIMAL" property="subItemQty" />
        <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType" />
        <result column="SITE_ADDRESS" jdbcType="VARCHAR" property="siteAddress" />
        <result column="USER_CNAME" jdbcType="VARCHAR" property="userCname" />
        <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
        <result column="SUB_PCB_VERSION" jdbcType="VARCHAR" property="subPcbVersion" />
        <result column="SUB_SOFTWARE" jdbcType="VARCHAR" property="subSoftware" />
        <result column="SUB_HARDLOGIC" jdbcType="VARCHAR" property="subHardlogic" />
        <result column="MAIN_PCB_VERSION" jdbcType="VARCHAR" property="mainPcbVersion" />
        <result column="MAIN_SOFTWARE" jdbcType="VARCHAR" property="mainSoftware" />
        <result column="MAIN_HARDLOGIC" jdbcType="VARCHAR" property="mainHardlogic" />
        <result column="SUB_LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="subLastUpdateDate" />
        <result column="SUB_LAST_UPDATED_BY" jdbcType="VARCHAR" property="subLastUpdatedBy" />
    </resultMap>

  <sql id="Base_Column_List">
    ASSEMBLE_LINES_ID, ENABLED_FLAG, CREATION_DATE, CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
    LAST_UPDATE_LOGIN, ASSEMBLE_HEADERS_ID, SCAN_TYPE, ITEM_BARCODE, ITEM_ID, ITEM_QTY, 
    ITEM_CODE, ITEM_NAME, ITEM_VERSION, ITEM_SERIES, POSITION_INFO, ATTRIBUTE1, ATTRIBUTE2, 
    ATTRIBUTE3, PCB_VERSION, SOFTWARE, HARDLOGIC, EXCEL_IMPORT_FLAG, EXCEL_ASSEMBLE_NAME, 
    SUPPLIER_NAME, VERIFY_FLAG
  </sql>


  <select id="getAssemblyMaterials"  resultType="com.zte.interfaces.dto.WsmAssembleLinesEntityDTO">
    select t.SCAN_TYPE,t.ITEM_BARCODE,t.ITEM_QTY,
    t.ITEM_CODE,t.ITEM_NAME
    from sfc.wsm_assemble_headers t1,sfc.wsm_assemble_lines t
    where t.assemble_headers_id = t1.assemble_headers_id
    and t.enabled_flag = 'Y' and t1.enabled_flag = 'Y'
    AND t1.ITEM_BARCODE= #{itemBarcode,jdbcType=VARCHAR}

    union all

    select t.BARCODE_TYPE SCAN_TYPE,t.ITEMBARCODE ITEM_BARCODE,t.ITEMQTY ITEM_QTY,
    t.ITEMCODE ITEM_CODE,t.ITEMNAME ITEM_NAME
    from sfc.wsm_assemable_imes_inf t
    where t.enabled_flag = 'Y' and t.mainbarcode =  #{itemBarcode,jdbcType=VARCHAR}
  </select>

  <select id="getAssemblyMaterialsWithEntityName"  resultType="com.zte.interfaces.dto.WsmAssembleLinesEntityDTO">
    select t.SCAN_TYPE,t.ITEM_BARCODE,t.ITEM_QTY,
    t.ITEM_CODE,t.ITEM_NAME,d.ENTITY_NAME,t.CREATION_DATE,t.CREATED_BY
    from sfc.wsm_assemble_headers t1,sfc.wsm_assemble_lines t,sfc.WSM_PLAN_SEQUENCE_INFO c,sfc.WSP_ENTITY_PLAN d
    where t.assemble_headers_id = t1.assemble_headers_id
    and t1.sequence_info_id = c.sequence_info_id
    and c.plan_id = d.plan_id
    and t.enabled_flag = 'Y' and t1.enabled_flag = 'Y'
    AND t1.ITEM_BARCODE= #{itemBarcode,jdbcType=VARCHAR}

    union all

    select t.BARCODE_TYPE SCAN_TYPE,t.ITEMBARCODE ITEM_BARCODE,t.ITEMQTY ITEM_QTY,
    t.ITEMCODE ITEM_CODE,t.ITEMNAME ITEM_NAME,t.WIP_ENTITY_NAME entityName,t.creation_date,t.created_by
    from sfc.wsm_assemable_imes_inf t
    where t.enabled_flag = 'Y' and t.mainbarcode =  #{itemBarcode,jdbcType=VARCHAR}
  </select>

  <select id="getAssemblyMaterialList" parameterType="java.util.List" resultType="com.zte.interfaces.dto.WsmAssembleLinesEntityDTO">
    select t.SCAN_TYPE,t.ITEM_BARCODE,t.ITEM_QTY,
    t.ITEM_CODE,t.ITEM_NAME
    from sfc.wsm_assemble_headers t1,sfc.wsm_assemble_lines t
    where t.assemble_headers_id = t1.assemble_headers_id
    and t.enabled_flag = 'Y' and t1.enabled_flag = 'Y'
    AND t1.ITEM_BARCODE IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

    union all

    select t.BARCODE_TYPE SCAN_TYPE,t.ITEMBARCODE ITEM_BARCODE,t.ITEMQTY ITEM_QTY,
    t.ITEMCODE ITEM_CODE,t.ITEMNAME ITEM_NAME
    from sfc.wsm_assemable_imes_inf t
    where t.enabled_flag = 'Y'
    and t.mainbarcode in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <!-- 新增自建任务号字段 -->
  <select id="getAssemblyMaterialListWithEntityName" parameterType="java.util.List" resultType="com.zte.interfaces.dto.ECWsmAssembleLinesEntityWithNameDTO">
    select t.SCAN_TYPE,t.ITEM_BARCODE,t.ITEM_QTY,
    t.ITEM_CODE,t.ITEM_NAME,d.ENTITY_NAME,t.CREATION_DATE,t.CREATED_BY
    from sfc.wsm_assemble_headers t1,sfc.wsm_assemble_lines t,sfc.WSM_PLAN_SEQUENCE_INFO c,sfc.WSP_ENTITY_PLAN d
    where t.assemble_headers_id = t1.assemble_headers_id
    and t1.sequence_info_id = c.sequence_info_id
    and c.plan_id = d.plan_id
    and t.enabled_flag = 'Y' and t1.enabled_flag = 'Y'
    AND t1.ITEM_BARCODE IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

    union all

    select t.BARCODE_TYPE SCAN_TYPE,t.ITEMBARCODE ITEM_BARCODE,t.ITEMQTY ITEM_QTY,
    t.ITEMCODE ITEM_CODE,t.ITEMNAME ITEM_NAME,t.WIP_ENTITY_NAME entityName,t.creation_date,t.created_by
    from sfc.wsm_assemable_imes_inf t
    where t.enabled_flag = 'Y'
    and t.mainbarcode in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
    
  <select id="selectMainBarcodeBySubBarcode" parameterType="com.zte.domain.model.datawb.WsmAssembleLines" resultMap="BaseResultMap">
		select b.ITEM_NAME as MAINNAME  --主物料名--
		,b.ITEM_BARCODE as MAINBARCODE  --主物料条码--
		,b.ITEM_CODE as MAIN_ITEMCODE --主物料代码--
		,b.ITEM_VERSION as MAIN_VERSION  --主物料版本--
		,a.ITEM_CODE as SUB_ITEMCODE--子物料代码--
		,a.ITEM_BARCODE as  SUB_ITEM_BARCODE--子物料条码--
		,a.ITEM_VERSION as SUB_ITEM_VERSION --子物料版本--
		,a.ITEM_SERIES as SUB_ITEM_SERIES --序列码--
		,a.ITEM_NAME as SUB_ITEM_NAME --子物料名称--
		,a.ITEM_QTY as  SUB_ITEM_QTY --数量--
		,b.PROD_BIGCATEGORY PRODUCT_TYPE  --产品大类 --
		,b.SITE_ADDRESS  --生产布点
		,a.SUPPLIER_NAME SUPPLY_NAME  --供应商
		,(g.user_cname || g.user_name) USER_CNAME --装配员--
		,a.CREATION_DATE  --装配时间--
		,a.PCB_VERSION SUB_PCB_VERSION  --子物料PCB版本--
		, a.SOFTWARE SUB_SOFTWARE --子物料软件程序--
		, a.HARDLOGIC SUB_HARDLOGIC  --子物料硬件逻辑--
		,b.PCB_VERSION MAIN_PCB_VERSION  --主物料PCB版本--
		, b.SOFTWARE MAIN_SOFTWARE  --主物料软件程序--
		, b.HARDLOGIC MAIN_HARDLOGIC  --主物料硬件逻辑
		from  
		WSM_ASSEMBLE_LINES a ,WSM_ASSEMBLE_HEADERS b ,bas_user_info g
		where 
		a.ASSEMBLE_HEADERS_ID=b.ASSEMBLE_HEADERS_ID
		and a.ENABLED_FLAG='Y'and b.ENABLED_FLAG='Y'
		and a.CREATED_BY=g.user_id(+)
        AND a.ITEM_BARCODE= #{subItemBarcode,jdbcType=VARCHAR}
  </select>
  
  <insert id="insertBatch" parameterType="java.util.List">
    insert into WSM_ASSEMBLE_LINES 
    (ASSEMBLE_LINES_ID, ENABLED_FLAG, CREATION_DATE, 
      CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
      LAST_UPDATE_LOGIN, ASSEMBLE_HEADERS_ID, SCAN_TYPE, 
      ITEM_BARCODE, ITEM_ID, ITEM_QTY, 
      ITEM_CODE, ITEM_NAME, ITEM_VERSION, 
      ITEM_SERIES, POSITION_INFO, ATTRIBUTE1, 
      ATTRIBUTE2, ATTRIBUTE3, PCB_VERSION, 
      SOFTWARE, HARDLOGIC, EXCEL_IMPORT_FLAG, 
      EXCEL_ASSEMBLE_NAME, SUPPLIER_NAME, VERIFY_FLAG
     )
    select WSM_ASSEMBLE_LINES_S.Nextval ASSEMBLE_LINES_ID,ENABLED_FLAG, CREATION_DATE, 
      CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
      LAST_UPDATE_LOGIN, ASSEMBLE_HEADERS_ID, SCAN_TYPE, 
      ITEM_BARCODE, ITEM_ID, ITEM_QTY, 
      ITEM_CODE, ITEM_NAME, ITEM_VERSION, 
      ITEM_SERIES, POSITION_INFO, ATTRIBUTE1, 
      ATTRIBUTE2, ATTRIBUTE3, PCB_VERSION, 
      SOFTWARE, HARDLOGIC, EXCEL_IMPORT_FLAG, 
      EXCEL_ASSEMBLE_NAME, SUPPLIER_NAME, VERIFY_FLAG
    from (
    <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
    select
      'Y' ENABLED_FLAG, SYSDATE CREATION_DATE, 
      #{item.createdBy,jdbcType=DECIMAL} CREATED_BY, SYSDATE LAST_UPDATE_DATE, #{item.lastUpdatedBy,jdbcType=DECIMAL} LAST_UPDATED_BY, 
      #{item.lastUpdateLogin,jdbcType=DECIMAL} LAST_UPDATE_LOGIN, #{item.assembleHeadersId,jdbcType=DECIMAL} ASSEMBLE_HEADERS_ID, #{item.scanType,jdbcType=VARCHAR} SCAN_TYPE, 
      #{item.itemBarcode,jdbcType=VARCHAR} ITEM_BARCODE, #{item.itemId,jdbcType=DECIMAL} ITEM_ID, #{item.itemQty,jdbcType=DECIMAL} ITEM_QTY, 
      #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE, #{item.itemName,jdbcType=VARCHAR} ITEM_NAME, #{item.itemVersion,jdbcType=VARCHAR} ITEM_VERSION, 
      #{item.itemSeries,jdbcType=VARCHAR} ITEM_SERIES, #{item.positionInfo,jdbcType=VARCHAR} POSITION_INFO, #{item.attribute1,jdbcType=VARCHAR} ATTRIBUTE1, 
      #{item.attribute2,jdbcType=VARCHAR} ATTRIBUTE2, #{item.attribute3,jdbcType=VARCHAR} ATTRIBUTE3, #{item.pcbVersion,jdbcType=VARCHAR} PCB_VERSION, 
      #{item.software,jdbcType=VARCHAR} SOFTWARE, #{item.hardlogic,jdbcType=VARCHAR} HARDLOGIC, #{item.excelImportFlag,jdbcType=VARCHAR} EXCEL_IMPORT_FLAG, 
      #{item.excelAssembleName,jdbcType=VARCHAR} EXCEL_ASSEMBLE_NAME, #{item.supplierName,jdbcType=VARCHAR} SUPPLIER_NAME, #{item.verifyFlag,jdbcType=VARCHAR} VERIFY_FLAG
     from dual
    </foreach>
    ) a
  </insert>
  
  <select id="getLineId" resultType="java.lang.String">
      select WSM_ASSEMBLE_LINES_S.Nextval from dual
  </select>

  <select id="getWsmAssembleLinesDTOPage" parameterType="java.util.Map" resultMap="BaseResultDTOMap">
    select * from
    (select u.*, rownum r from
    (select
        wah.ITEM_BARCODE AS MAIN_ITEM_BARCODE, wah.ITEM_CODE AS MAIN_ITEM_CODE, wah.ITEM_NAME AS MAIN_ITEM_NAME,
        wal.ITEM_BARCODE AS SUB_ITEM_BARCODE, wal.ITEM_CODE AS SUB_ITEM_CODE, wal.ITEM_NAME AS SUB_ITEM_NAME,
        wal.LAST_UPDATE_DATE AS SUB_LAST_UPDATE_DATE
    from WSM_ASSEMBLE_LINES wal
    left join WSM_ASSEMBLE_HEADERS wah on  wal.ASSEMBLE_HEADERS_ID = wah.ASSEMBLE_HEADERS_ID
    where wal.ENABLED_FLAG = 'Y'
    AND wah.ENABLED_FLAG = 'Y'
    <if test="mainItemBarcode != null and mainItemBarcode != ''"> and wah.ITEM_BARCODE = #{mainItemBarcode,jdbcType=VARCHAR} </if>
    <if test="subItemBarcode != null and subItemBarcode != ''"> and wal.ITEM_BARCODE = #{subItemBarcode,jdbcType=VARCHAR} </if>
    order by wal.ITEM_BARCODE) u
    where rownum &lt;= #{endRow})
    where r >= #{startRow}
  </select>

  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getWsmAssembleLinesDTOCount" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(*)
    from WSM_ASSEMBLE_LINES wal
    left join WSM_ASSEMBLE_HEADERS wah on  wal.ASSEMBLE_HEADERS_ID = wah.ASSEMBLE_HEADERS_ID
    where wal.ENABLED_FLAG = 'Y'
    AND wah.ENABLED_FLAG = 'Y'
    <if test="mainItemBarcode != null and mainItemBarcode != ''"> and wah.ITEM_BARCODE = #{mainItemBarcode,jdbcType=VARCHAR} </if>
    <if test="subItemBarcode != null and subItemBarcode != ''"> and wal.ITEM_BARCODE = #{subItemBarcode,jdbcType=VARCHAR} </if>
  </select>


  <!-- 数据字典 -->
  <select id="getSysLookupValues"  resultType="com.zte.domain.model.SysLookupValues">
    select *
    from MESSYS.sys_lookup_values t
    where t.ENABLED_FLAG = 'Y'
    AND  t.LOOKUP_TYPE = #{lookupType}
  </select>


  <select id="getAssemblyMaterialsByEntityId"   resultType="com.zte.interfaces.dto.CpmConfigItemAssembleDTO">
    select ENTITY_ID,ITEM_BARCODE,BARCODE_TYPE,BARCODE_QTY,ITEM_CODE,ITEM_ID,ITEM_NAME
    from SFC.cpm_config_item_assemble t
    where t.entity_id = #{entityId}
    and t.enabled_flag = 'Y'
  </select>

  <select id="getAssemblyMaterialsByServerSn"   resultType="com.zte.interfaces.dto.CpmConfigItemAssembleDTO">
    select ENTITY_ID,ITEM_BARCODE,BARCODE_TYPE,BARCODE_QTY,ITEM_CODE,ITEM_ID,ITEM_NAME
    from SFC.cpm_config_item_assemble t
    where t.ITEM_BARCODE = #{serverSn,jdbcType=VARCHAR}
    and t.enabled_flag = 'Y'
  </select>

  <select id="getAssemblyMaterialsByEntityName" resultType="com.zte.interfaces.dto.CpmConfigItemAssembleDTO" parameterType="java.util.Map">
    select t.ENTITY_ID,ITEM_BARCODE,BARCODE_TYPE,BARCODE_QTY,ITEM_CODE,ITEM_ID,ITEM_NAME
    from SFC.cpm_config_item_assemble t,
         sfc.cpm_contract_entities cce
    where cce.entity_id= t.entity_id
          and t.enabled_flag='Y'
          and cce.entity_name=#{entityName,jdbcType=VARCHAR}
          and t.ITEM_CODE =#{ZteCode,jdbcType=VARCHAR}
  </select>

  <select id ="getZmsQualityCode" resultType="java.lang.String" parameterType="java.util.List">
    select server_sn||','||quality_code
    from sfc.zms_quality_code zqc
    where  zqc.enabled_flag='Y' and zqc.server_sn in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
    and rownum <![CDATA[<=]]> 500
  </select>

  <select id="getZmsServerSn" resultType="java.lang.String" parameterType="java.util.List">
    select distinct to_char(SERVER_SN)
    from SFC.ZMS_COMPLETE_MACHINE_HEAD zcmh
    where  zcmh.SERVER_SN in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
    and rownum <![CDATA[<=]]> 500
  </select>

  <insert id="insertZmsQualityCode" parameterType="com.zte.interfaces.dto.QualityCodeOutputDTO">
    merge into sfc.zms_quality_code T
    using (select #{serverSn,jdbcType=VARCHAR} server_sn,#{qualityCode,jdbcType=VARCHAR} quality_code
    from dual) A
    ON (T.server_sn = A.server_sn)
    when matched then
    update
    set T.quality_code = A.quality_code,
    T.last_update_date = sysdate
    when not matched then
    insert
    (server_sn, quality_code, create_date, last_update_date, enabled_flag)
    values
    (A.server_sn, A.quality_code, sysdate, sysdate, 'Y')
  </insert>

  <insert id="batchInsertZmsQualityCodeLog" parameterType="com.zte.interfaces.dto.InternetCustomerQaCtrlOutDTO">
    <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
      insert into sfc.zms_quality_code_log
      (pid,
      server_sn,
      quality_code,
      entity_Name,
      customer_Name,
      creation_date,
      last_update_date,
      enabled_flag)
      values(
      #{item.pId,jdbcType=VARCHAR},
      #{item.sn,jdbcType=VARCHAR},
      #{item.qualityCode,jdbcType=VARCHAR},
      #{item.taskno,jdbcType=VARCHAR},
      #{item.customerName,jdbcType=VARCHAR},
      SYSDATE ,
      SYSDATE,
      'Y')
    </foreach>
  </insert>

  <insert id="updateZmsQualityCodeLog" parameterType="com.zte.interfaces.dto.InternetCustomerQaCtrlOutDTO">
    update sfc.zms_quality_code_log a
    set a.err_cause = #{reason,jdbcType=VARCHAR},
    a.last_update_date =sysdate
    where a.pid = #{pId,jdbcType=VARCHAR}
    and (a.server_sn = #{sn,jdbcType=VARCHAR} or a.entity_name = #{taskno,jdbcType=VARCHAR})
  </insert>

  <select id="selectMainLogBySn" parameterType="list" resultType="com.zte.interfaces.dto.ZmsInternetMainDTO">
    SELECT DISTINCT m.chassis_sn AS server_sn, m.contract_number, m.entity_id, m.entity_name
      FROM wmes.zms_overall_unit_meituan m
     WHERE m.enabled_flag = 'Y'
       AND m.chassis_sn in
    <foreach collection="snList" index="index" item="sn" open="(" separator="," close=")">
      #{sn,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getCustomerPONumber"   resultType="java.lang.String">
    SELECT
    decode(length(TRIM(case when ccms.device_site_id is null and ccms.devices_id is null then (WMES.FIND_CLIENTPO_BY_MFGSITE(ccms.MFG_SITE_ID))
    else (WMES.FIND_CLIENTPO_BY_MFGSITE_NEW(ccms.MFG_SITE_ID)) end)),
    null,
    cch.CUSTOMER_PO,
    (case when ccms.device_site_id is null and ccms.devices_id is null then (WMES.FIND_CLIENTPO_BY_MFGSITE(ccms.MFG_SITE_ID))
    else (WMES.FIND_CLIENTPO_BY_MFGSITE_NEW(ccms.MFG_SITE_ID)) end)
    )
    FROM SFC.CDM_CONTRACT_HEADERS cch,
    SFC.CDM_CONTRACT_LINES ccl,
    SFC.CPM_CONTRACT_ENTITIES cce,
    SFC.CPM_CONTRACT_MFG_SITES ccms
    WHERE ccms.ENTITY_ID=cce.entity_id
    AND ccms.CONTRACT_LINE_ID=ccl.contract_line_id
    AND ccl.CONTRACT_HEADER_ID=cch.contract_header_id
    AND ccms.enabled_flag='Y'
    AND cce.ENABLED_FLAG='Y'
    AND ccl.enabled_flag='Y'
    and cce.ENTITY_ID = #{entityId}
    and rownum = 1
  </select>

  <select id="getWsmAssemblyListByMainAndSub" parameterType="com.zte.interfaces.dto.WsmAssembleLinesDTO"  resultMap="BaseResultDTOMap">
    select
    wah.ASSEMBLE_HEADERS_ID AS ASSEMBLE_HEADERS_ID,
    wah.ITEM_BARCODE AS MAIN_ITEM_BARCODE, wah.ITEM_CODE AS MAIN_ITEM_CODE, wah.ITEM_NAME AS MAIN_ITEM_NAME,
    wal.ITEM_BARCODE AS SUB_ITEM_BARCODE, wal.ITEM_CODE AS SUB_ITEM_CODE, wal.ITEM_NAME AS SUB_ITEM_NAME,
    wal.LAST_UPDATE_DATE AS SUB_LAST_UPDATE_DATE
    from WSM_ASSEMBLE_HEADERS wah
    inner join WSM_ASSEMBLE_LINES wal on wal.ASSEMBLE_HEADERS_ID = wah.ASSEMBLE_HEADERS_ID
    where wal.ENABLED_FLAG = 'Y'
    and wah.ENABLED_FLAG = 'Y'
    <if test="sourceSys != null and sourceSys != ''"> and wal.SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR} </if>
    <if test="mainItemBarcode != null and mainItemBarcode != ''"> and wah.ITEM_BARCODE = #{mainItemBarcode,jdbcType=VARCHAR} </if>
    <if test="subItemBarcode != null and subItemBarcode != ''"> and wal.ITEM_BARCODE = #{subItemBarcode,jdbcType=VARCHAR} </if>
    <if test="mainItemBarcodeList != null and mainItemBarcodeList.size() > 0">
      and wah.ITEM_BARCODE IN
      <foreach collection="mainItemBarcodeList" index="index" item="item" open="(" separator="," close=")">
        #{mainItemBarcodeList[${index}]}
      </foreach>
    </if>
    <if test="subItemBarcodeList != null and subItemBarcodeList.size() > 0">
      and wal.ITEM_BARCODE IN
      <foreach collection="subItemBarcodeList" index="index" item="item" open="(" separator="," close=")">
        #{subItemBarcodeList[${index}]}
      </foreach>
    </if>
    <if test="fullBarcodeList != null and fullBarcodeList.size() > 0">
      and wah.ITEM_BARCODE || '_' || wal.ITEM_BARCODE IN
      <foreach collection="fullBarcodeList" index="index" item="item" open="(" separator="," close=")">
        #{fullBarcodeList[${index}]}
      </foreach>
    </if>
  </select>

  <delete id="removeWsmAssemblyListByMainAndSub" parameterType="java.util.Map" >
    delete from WSM_ASSEMBLE_LINES wal
    where wal.ENABLED_FLAG = 'Y'
    and wal.SOURCE_SYS = 'iMES'
    <if test="mainItemBarcode != null and mainItemBarcode != ''">
      and wal.ASSEMBLE_HEADERS_ID in
      (select ASSEMBLE_HEADERS_ID from WSM_ASSEMBLE_HEADERS wah where wah.ENABLED_FLAG = 'Y'
      and wah.ITEM_BARCODE = #{mainItemBarcode,jdbcType=VARCHAR})
    </if>
    <if test="subItemBarcodeList != null and subItemBarcodeList.size() > 0">
      AND wal.ITEM_BARCODE IN
      <foreach collection="subItemBarcodeList" index="index" item="item" open="(" separator="," close=")">
        #{subItemBarcodeList[${index}]}
      </foreach>
    </if>
    <if test="(subItemBarcodeList == null or subItemBarcodeList.size() == 0 or mainItemBarcode == null or mainItemBarcode == '')">
      AND 1=2
    </if>
  </delete>

  <insert id="batchInsertAssemble" parameterType="java.util.List">
    insert into WSM_ASSEMBLE_LINES
    (ASSEMBLE_LINES_ID, ENABLED_FLAG, CREATION_DATE,
    CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY,
    LAST_UPDATE_LOGIN, ASSEMBLE_HEADERS_ID, SCAN_TYPE,
    ITEM_BARCODE, ITEM_ID, ITEM_QTY,
    ITEM_CODE, ITEM_NAME, ITEM_VERSION,
    ITEM_SERIES, POSITION_INFO, ATTRIBUTE1,
    ATTRIBUTE2, ATTRIBUTE3, PCB_VERSION,
    SOFTWARE, HARDLOGIC, EXCEL_IMPORT_FLAG,
    EXCEL_ASSEMBLE_NAME, SUPPLIER_NAME, VERIFY_FLAG,SOURCE_SYS
    )
    select WSM_ASSEMBLE_LINES_S.Nextval ASSEMBLE_LINES_ID,ENABLED_FLAG, CREATION_DATE,
    CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY,
    LAST_UPDATE_LOGIN, ASSEMBLE_HEADERS_ID, SCAN_TYPE,
    ITEM_BARCODE, ITEM_ID, ITEM_QTY,
    ITEM_CODE, ITEM_NAME, ITEM_VERSION,
    ITEM_SERIES, POSITION_INFO, ATTRIBUTE1,
    ATTRIBUTE2, ATTRIBUTE3, PCB_VERSION,
    SOFTWARE, HARDLOGIC, EXCEL_IMPORT_FLAG,
    EXCEL_ASSEMBLE_NAME, SUPPLIER_NAME, VERIFY_FLAG,SOURCE_SYS
    from (
    <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
      select
      'Y' ENABLED_FLAG, SYSDATE CREATION_DATE,
      #{item.createdBy,jdbcType=DECIMAL} CREATED_BY, SYSDATE LAST_UPDATE_DATE, #{item.lastUpdatedBy,jdbcType=DECIMAL} LAST_UPDATED_BY,
      #{item.lastUpdateLogin,jdbcType=DECIMAL} LAST_UPDATE_LOGIN, #{item.assembleHeadersId,jdbcType=DECIMAL} ASSEMBLE_HEADERS_ID, #{item.scanType,jdbcType=VARCHAR} SCAN_TYPE,
      #{item.itemBarcode,jdbcType=VARCHAR} ITEM_BARCODE, #{item.itemId,jdbcType=DECIMAL} ITEM_ID, #{item.itemQty,jdbcType=DECIMAL} ITEM_QTY,
      #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE, #{item.itemName,jdbcType=VARCHAR} ITEM_NAME, #{item.itemVersion,jdbcType=VARCHAR} ITEM_VERSION,
      #{item.itemSeries,jdbcType=VARCHAR} ITEM_SERIES, #{item.positionInfo,jdbcType=VARCHAR} POSITION_INFO, #{item.attribute1,jdbcType=VARCHAR} ATTRIBUTE1,
      #{item.attribute2,jdbcType=VARCHAR} ATTRIBUTE2, #{item.attribute3,jdbcType=VARCHAR} ATTRIBUTE3, #{item.pcbVersion,jdbcType=VARCHAR} PCB_VERSION,
      #{item.software,jdbcType=VARCHAR} SOFTWARE, #{item.hardlogic,jdbcType=VARCHAR} HARDLOGIC, #{item.excelImportFlag,jdbcType=VARCHAR} EXCEL_IMPORT_FLAG,
      #{item.excelAssembleName,jdbcType=VARCHAR} EXCEL_ASSEMBLE_NAME, #{item.supplierName,jdbcType=VARCHAR} SUPPLIER_NAME, #{item.verifyFlag,jdbcType=VARCHAR} VERIFY_FLAG,
      #{item.sourceSys,jdbcType=VARCHAR} SOURCE_SYS
      from dual
    </foreach>
    ) a
    where not exists( select 1 from WSM_ASSEMBLE_LINES t where  t.ENABLED_FLAG = 'Y' and t.ASSEMBLE_HEADERS_ID = a.ASSEMBLE_HEADERS_ID  and t.ITEM_BARCODE = a.ITEM_BARCODE )
  </insert>

  <delete id="deleteAssembleLineForRemoveRepeat" parameterType="java.util.Map" >
    delete from WSM_ASSEMBLE_LINES wal
    where wal.ENABLED_FLAG = 'Y'
    and wal.SOURCE_SYS = 'iMES'
    and (assemble_headers_id ,item_barcode) in
    (select assemble_headers_id ,item_barcode from wsm_assemble_lines where enabled_flag = 'Y' and source_sys = 'iMES'
    group by assemble_headers_id ,item_barcode having count(assemble_headers_id ) > 1)
    AND ASSEMBLE_LINES_ID NOT IN (SELECT MIN(ASSEMBLE_LINES_ID) FROM wsm_assemble_lines
    where enabled_flag = 'Y' and source_sys = 'iMES'
    group by assemble_headers_id ,item_barcode having count(assemble_headers_id ) >1)
  </delete>

</mapper>