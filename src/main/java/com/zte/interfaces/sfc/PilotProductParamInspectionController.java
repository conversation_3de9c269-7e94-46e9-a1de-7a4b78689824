package com.zte.interfaces.sfc;

import com.zte.application.sfc.PilotProductParamInspectionService;
import com.zte.interfaces.dto.sfc.PilotProductReqDTO;
import com.zte.interfaces.dto.sfc.PilotProductReqVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 中试个参检验接口开发
 */
@RestController
@RequestMapping("/pilotProduct")
public class PilotProductParamInspectionController {
    @Autowired
    private PilotProductParamInspectionService pilotProductParamInspectionService;


    @ApiOperation("校验")
    @PostMapping("paramCheck")
    public ServiceData<List<PilotProductReqVO>> paramCheck(@RequestBody @Validated PilotProductReqDTO reqDTO) throws MesBusinessException {
        return ServiceDataBuilderUtil.success(pilotProductParamInspectionService.dataCheckIsOnly(reqDTO));

    }

    @RecordLogAnnotation("获取CPE发货数量")
    @ApiOperation("获取CPE发货数量")
    @PostMapping(value = "/getCpeSnListCount", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData getSnListCount(@RequestBody List<String> snStartList,
                                      @RequestParam(required = false) Integer daysAgoStart,
                                      @RequestParam(required = false) Integer daysAgoEnd) {
        return ServiceDataBuilderUtil.success(pilotProductParamInspectionService.getCpeSnListCount(snStartList,
                daysAgoStart, daysAgoEnd));
    }

    @RecordLogAnnotation("获取CPE发货SN")
    @ApiOperation("获取CPE发货SN")
    @PostMapping(value = "/getCpeSnListBySnStart", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData getCpeSnListBySnStart(@RequestBody String snStart,
                                             @RequestParam(required = false) Integer daysAgoStart,
                                             @RequestParam(required = false) Integer daysAgoEnd) {
        return ServiceDataBuilderUtil.success(pilotProductParamInspectionService.getCpeSnListBySnStart(snStart,
                daysAgoStart, daysAgoEnd));
    }
}
