package com.zte.interfaces;

import com.zte.application.datawb.StbProdDeliveryInfoQueryService;
import com.zte.application.datawb.StbProducteInfoDataService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.StbProdDeliveryInfoQuery;
import com.zte.interfaces.dto.StbProdDeliveryInfoQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.Export;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 机顶盒产品发货信息查询Controller
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/StbProdDeliveryInfoQuery")
@Api(description = "机顶盒产品发货信息查询API")
public class StbProdDeliveryInfoQueryController {

	@Autowired
	private StbProdDeliveryInfoQueryService stbProdDeliveryInfoQueryService;

    @Autowired
    private StbProducteInfoDataService stbProducteInfoDataService;

	private static final Logger logger = LoggerFactory.getLogger(OemProduceSkipGoodsQueryController.class);
	
	/**
	 * 合同号，发货指令号，装箱单号 查询
	 * @param request
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@ApiOperation("合同号，发货指令号，装箱单号查询机顶盒发货信息查询")
	@ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
		@ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
	@RequestMapping(value = "/getList", method = RequestMethod.GET, 
		produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData<?> getList(HttpServletRequest request, StbProdDeliveryInfoQueryDTO dto) throws Exception {

		List<StbProdDeliveryInfoQuery> list = stbProdDeliveryInfoQueryService.getPage(dto);
		PageRows<StbProdDeliveryInfoQuery> page = new PageRows<StbProdDeliveryInfoQuery>();
		if(list !=null && list.size()>0){
			page.setCurrent(dto.getPage());
			page.setTotal(dto.getTotal());
			page.setRows(list);
		}
		// 返回统一的服务端数据
		ServiceData<PageRows<StbProdDeliveryInfoQuery>> ret = new ServiceData<PageRows<StbProdDeliveryInfoQuery>>();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(page);
		return ret;
	}
	
	/**
	 * excel导入查询条件
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@ApiOperation("excel导入")
	@RequestMapping(value = "/Upload", method = RequestMethod.POST, 
		produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData<?> resolveExcel(StbProdDeliveryInfoQueryDTO dto) throws Exception {
		ServiceData<?> ret = stbProdDeliveryInfoQueryService.uploadExcel(dto);   
		return ret;
	}

	/**
	 * 批量查询
	 * @param request
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@ApiOperation("合同号，发货指令号，装箱单号查询机顶盒发货信息批量查询")
	@ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
		@ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
	@RequestMapping(value = "/getListBatch", method = RequestMethod.POST, 
		produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData<?> getListBatch(HttpServletRequest request, 
			@RequestBody StbProdDeliveryInfoQueryDTO dto) throws Exception {
	    List<StbProdDeliveryInfoQuery> list = stbProdDeliveryInfoQueryService.getPage(dto);
	    PageRows<StbProdDeliveryInfoQuery> page = new PageRows<StbProdDeliveryInfoQuery>();
	    if(CollectionUtils.isNotEmpty(list)){
	      page.setCurrent(dto.getPage());
	      page.setTotal(dto.getTotal());
	      page.setRows(list);
	    }
	    // 返回统一的服务端数据
	    ServiceData<PageRows<StbProdDeliveryInfoQuery>> ret = new ServiceData<PageRows<StbProdDeliveryInfoQuery>>();
	    ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
	    ret.setBo(page);
	    return ret;
	}

	/**
	 * 导出excel
	 * @param request
	 * @param response
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@ApiOperation("导出excel")
	@Export
	@RequestMapping(value = "/export", 
		produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	@TransmittableHeader
	public ServiceData<?> exportExcel(HttpServletRequest request, HttpServletResponse response,
	        @RequestBody StbProdDeliveryInfoQueryDTO dto) throws Exception{
		stbProdDeliveryInfoQueryService.createExcel(dto);
	    ServiceData<String> ret = new ServiceData<>();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(CommonUtils.getLmbMessage(MessageId.THE_EXCEL_BEING_GENERATED));
		return ret;
	}
	
	/**
	 * 清除服务器生成的文件
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@ApiOperation("清除文件服务器生成的机顶盒excel文件")
	@RequestMapping(value = "/removeServerFile", 
		produces = MediaType.APPLICATION_JSON_VALUE)
	public void removeServerFile(HttpServletRequest request, HttpServletResponse response) throws Exception{
		try {
			stbProdDeliveryInfoQueryService.romoveServerFile();
		} catch (Exception e) {
			  logger.info("清除文件失败：" + e.getMessage());
		}	  
	}


    @RecordLogAnnotation("获取Dhome发货数量")
    @ApiOperation("获取Dhome发货数量")
    @PostMapping(value = "/getDhomeSnListCount", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData getDhomeSnListCount(@RequestBody List<String> snStartList,
                                           @RequestParam(required = false) Integer daysAgoStart,
                                           @RequestParam(required = false) Integer daysAgoEnd) {
        return ServiceDataBuilderUtil.success(stbProducteInfoDataService.getDhomeSnListCount(snStartList, daysAgoStart
                , daysAgoEnd));
    }

	@RecordLogAnnotation("获取Dhome发货SN")
	@ApiOperation("获取Dhome发货SN")
	@PostMapping(value = "/getDhomeSnListBySnStart", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ServiceData getDhomeSnListBySnStart(@RequestBody String snStart,
										   @RequestParam(required = false) Integer daysAgoStart,
										   @RequestParam(required = false) Integer daysAgoEnd) {
		return ServiceDataBuilderUtil.success(stbProducteInfoDataService.getDhomeSnListBySnStart(snStart, daysAgoStart
				, daysAgoEnd));
	}
}
