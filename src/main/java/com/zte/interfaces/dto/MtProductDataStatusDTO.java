package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class MtProductDataStatusDTO implements Serializable {
    private String memo;
    private String status;
    private String preStatus;
    private Integer preToS;
    private Integer complateCount;
    private Integer totalCount;
    private Date createDate;
    private Date lastUpdateDate;
    private Integer isAdd;

    @Override
    public String toString() {
        return "MTProductDataStatusDTO{" +
                "memo=" + memo +
                ", status=" + status +
                ", preStatus=" + preStatus +
                ", preToS=" + preToS +
                ", complateCount=" + complateCount +
                ", totalCount=" + totalCount +
                ", createDate=" + createDate +
                ", lastUpdateDate=" + lastUpdateDate +
                ", isAdd=" + isAdd +
                '}';
    }
}


