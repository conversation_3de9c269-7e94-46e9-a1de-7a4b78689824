package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 配置物料装配信息
 *
 * <AUTHOR>
 * @date 2025-08-04 10:20:00
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("配置物料装配信息DTO")
public class ECMaterialAssemblyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String entityName;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String orgId;

    /**
     * 服务器SN
     */
    @ApiModelProperty(value = "服务器SN")
    private String serverSn;

    /**
     * 明细列表
     */
    @ApiModelProperty(value = "明细列表")
    private List<ECMaterialAssemblyItemDTO> assembleList;

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getServerSn() {
        return serverSn;
    }

    public void setServerSn(String serverSn) {
        this.serverSn = serverSn;
    }

    public List<ECMaterialAssemblyItemDTO> getAssembleList() {
        return assembleList;
    }

    public void setAssembleList(List<ECMaterialAssemblyItemDTO> assembleList) {
        this.assembleList = assembleList;
    }
}
