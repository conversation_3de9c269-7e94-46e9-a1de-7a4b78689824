package com.zte.domain.model.datawb;

import java.util.Collection;
import java.util.List;

import com.zte.interfaces.dto.sfc.PilotProductDTO;
import com.zte.interfaces.dto.sfc.PilotProductParamVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StbProducteInfoDataRepository {

	/**
	 * 机顶盒发货信息SFC.STB_PRODUCTE_INFO表字段查询
	 *
	 * @param barcodes
	 * @return
	 */
	List<StbProdDeliveryInfoQueryExtend> getListStbProdDeliveryInfo(@Param("barcodes") String barcodes);

	/**
	 * 根据条件汇总对应数据
	 *
	 * @param pilotProductParamDTO
	 * @return
	 */
	List<PilotProductParamVO> getProductSumCpeList(PilotProductDTO pilotProductParamDTO);
	/**
	 * 根据条件汇总对应数据
	 *
	 * @param pilotProductParamDTO
	 * @return
	 */
	List<PilotProductParamVO> getProductSumMacCpeList(PilotProductDTO pilotProductParamDTO);

    List<StbProductSnCount> getDhomeSnCount(@Param("snStartList") List<String> snStartList, @Param("daysAgoStart") Integer daysAgoStart, @Param("daysAgoEnd") Integer daysAgoEnd);

    List<StbProductSnCount> getDhomeSnCountByProductSnList(@Param("snStartList") List<String> snStartList,
                                                           @Param("productSnList") List<String> productSnList);

	List<String> getDhomeSnListBySnStart(@Param("snStart") String snStart, @Param("daysAgoStart") Integer daysAgoStart, @Param("daysAgoEnd") Integer daysAgoEnd);

	List<String> getDhomeSnListByItemList(@Param("snStart") String snStart, @Param("itemBarcodes") List<String> itemBarcodes);
}
