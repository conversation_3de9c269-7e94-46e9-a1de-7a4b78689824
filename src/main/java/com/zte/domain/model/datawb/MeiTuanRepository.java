package com.zte.domain.model.datawb;

import com.zte.interfaces.dto.MTEntitiesStatusDTO;
import com.zte.interfaces.dto.MtProductDataStatusDTO;
import com.zte.interfaces.dto.MTProductDataUploadLogDTO;
import com.zte.interfaces.dto.OIDDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 美团仓储类
 *
 * <AUTHOR>
 */
@Mapper
public interface MeiTuanRepository {
    /* Started by AICoder, pid:d243ere7e2u795c14e5a0864302b7c317e678982 */

    /**
     * 获取生产数据
     *
     * @param dto
     * @return
     */
    public List<MTProductDataUploadLogDTO> getProductDataUpload(OIDDto dto);

    /**
     * 查询美团状态
     *
     * @param dto
     * @return
     */
    public List<MtProductDataStatusDTO> getProductDataStatus(OIDDto dto);

    /**
     * 查询任务状态
     *
     * @param oid
     * @return
     */
    public List<MTEntitiesStatusDTO> getEntitiesStatus(@Param("oid") String oid);

    /**
     * 查询OID
     *
     * @param dto
     * @return
     */
    public List<String> getProductOID(OIDDto dto);

    /**
     * 获取历史生产数据
     *
     * @param dto
     * @return
     */
    public List<MTProductDataUploadLogDTO> getHistoryProductDataUpload(OIDDto dto);

    /**
     * 插入历史生产数据
     *
     * @param list
     * @return
     */
    public void insertHistoryProductDataUpload(@Param("list") List<MTProductDataUploadLogDTO> list);

    /**
     * 更新历史生产数据
     *
     * @param list
     * @return
     */
    void batchUpdateHistoryProductDataUpload(List<MTProductDataUploadLogDTO> list);

    /**
     * 更新MEITUAN_PRODUCTDATASTATUS
     *
     * @param list
     */
    void batchUpdateProductDataStatusDTO(List<MtProductDataStatusDTO> list);

    /**
     * 插入MEITUAN_PRODUCTDATASTATUS
     *
     * @param list
     */
    void insertMtProductDataStatusDTO(@Param("list") List<MtProductDataStatusDTO> list);


    /* Ended by AICoder, pid:d243ere7e2u795c14e5a0864302b7c317e678982 */
}
