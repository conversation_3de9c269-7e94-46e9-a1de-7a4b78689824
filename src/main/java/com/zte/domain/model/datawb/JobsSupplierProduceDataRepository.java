package com.zte.domain.model.datawb;

import java.util.Collection;
import java.util.List;

import com.zte.interfaces.dto.sfc.PilotProductDTO;
import com.zte.interfaces.dto.sfc.PilotProductParamVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface JobsSupplierProduceDataRepository {

  /**
     * 项目名称 : OEM产品发货信息查询分析
     * <AUTHOR>
     * @return
  */
  List<OemProduceSkipGoodsQuery> getOemQueryList(@Param("barCode")  String barCode);


  /**
   * 根据条件汇总对应数据
   *
   * @param pilotProductParamDTO
   * @return
   */
  List<PilotProductParamVO> getProductSumList(PilotProductDTO pilotProductParamDTO);
  /**
   * 根据条件汇总对应数据
   *
   * @param pilotProductParamDTO
   * @return
   */
  List<PilotProductParamVO> getProductSumMacList(PilotProductDTO pilotProductParamDTO);

  List<StbProductSnCount> getCpeSnCount(@Param("snStartList") List<String> snStartList,@Param("daysAgoStart") Integer daysAgoStart, @Param("daysAgoEnd") Integer daysAgoEnd);
  List<StbProductSnCount> getCpeSnCountByItemBarcodes(@Param("snStartList") List<String> snStartList,@Param("itemBarcodes") List<String> itemBarcodes);

    List<String> getCpeSnListBySnStart(@Param("snStart") String snStart, @Param("daysAgoStart") Integer daysAgoStart, @Param("daysAgoEnd") Integer daysAgoEnd);

    List<String> getCpeSnListByItemBarcodes(@Param("snStart") String snStart, @Param("itemBarcodes") List<String> itemBarcodes);
}