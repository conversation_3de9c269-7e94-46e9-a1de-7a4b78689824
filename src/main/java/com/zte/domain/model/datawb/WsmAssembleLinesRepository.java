/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.domain.model.datawb;

import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WsmAssembleLinesWriteBack;
import com.zte.interfaces.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Mapper
public interface WsmAssembleLinesRepository {

    /**
     * 增加实体数据
     * 
     * @param record
     * @return WsmAssembleLines
     **/
    List<WsmAssembleLines> selectMainBarcodeBySubBarcode(WsmAssembleLines record);

    List<WsmAssembleLinesEntityDTO> getAssemblyMaterials(@Param("itemBarcode") String itemBarcode);
    List<ECWsmAssembleLinesEntityWithNameDTO> getAssemblyMaterialsWithEntityName(@Param("itemBarcode") String itemBarcode);
    List<WsmAssembleLinesEntityDTO> getAssemblyMaterialList(List<String> itemBarcodeList);
    List<ECWsmAssembleLinesEntityWithNameDTO> getAssemblyMaterialListWithEntityName(List<String> itemBarcodeList);

    int insertBatch(List<WsmAssembleLinesWriteBack> recode);

    /**
     * @param map
     * @return
     * <AUTHOR> 袁海洋
     */
    List<WsmAssembleLinesDTO> getWsmAssembleLinesDTOPage(Map<String, Object> map);

    Long getWsmAssembleLinesDTOCount(Map<String, Object> record);

    List<SysLookupValues> getSysLookupValues(@Param("lookupType") String lookupType);

    List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByEntityId(@Param("entityId") Integer entityId);

    List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByServerSn(@Param("serverSn") String serverSn);

    /**
     * 根据任务号获取装配物料-整机数据回传专用
     * @param record
     * @return
     */
    List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByEntityName(Map<String, Object> record);

    /**
     * 写入质量码表
     * @param dto
     * @return
     */
    int insertZmsQualityCode(QualityCodeOutputDTO dto);

    /**
     * 批量写入日志
     * @param list
     * @return
     */
    int batchInsertZmsQualityCodeLog(List<InternetCustomerQaCtrlOutDTO> list);

    /**
     * 更新日志
     * @param dto
     * @return
     */
    int updateZmsQualityCodeLog(InternetCustomerQaCtrlOutDTO dto);

    /**
     * 获取质量码
     * @param list
     * @return
     */
    List<String> getZmsQualityCode(List<String> list);

    /**
     * 批量判断质量码存在
     * @param list
     * @return
     */
    List<String> getZmsServerSn(List<String> list);

    List<ZmsInternetMainDTO> selectMainLogBySn(@Param("snList") List<String> snList);


    String getCustomerPONumber(@Param("entityId") Integer entityId);

    /**
     * 根据主子条码集合查询所有装配关系
     * @param paramDTO
     * @return
     */
    List<WsmAssembleLinesDTO> getWsmAssemblyListByMainAndSub(WsmAssembleLinesDTO paramDTO);

    /**
     * 根据主子条码解除装配关系-推送装配关系到MES专用
     * @param map
     * @return
     */
    int removeWsmAssemblyListByMainAndSub(Map<String, Object> map);
    /**
     * 根据主子条码新增装配关系-推送装配关系到MES专用
     * @param recode
     * @return
     */
    int batchInsertAssemble(List<WsmAssembleLinesWriteBack> recode);
    /**
     * 删除推送的重复装配关系，只保留一条
     * @param
     * @return
     */
    int deleteAssembleLineForRemoveRepeat();
}